export default {
  line: "Line",
  language: "Language",
  signin:"Sign in",
  baozhengjin:"保证金",
  unbaozhengjin:"解冻保证金",
  common: [
    "Service",
    "Cancel Suspension",
    "Lucky draw",
    "Notification",
    "Settings",
    "Help",
    "About",
    "Confirm"
  ],
  upload: [
    "Uploading...",
    "Wrong format",
    "Uploaded successfully",
    "Upload failed"
  ],
  appMsg: [
    "Ready to download updates...",
    "The download is complete. Install the update package?",
    "Download update failed",
    "{num}% downloaded",
    "Updating...",
    "The update is successful and will restart soon",
    "Update failed",
    "Press again to exit the application"
  ],
  vanPull: [
    "No more data",
    "No data"
  ],
  login: {
    text: [
      "Remember username/password",
      "Logging...",
      "Log in now",
      "Log in"
    ],
    placeholder: [
      "Please input phone number or email",
      "Please enter login password"
    ],
    i18n: [
      "No {title} account? {a} {line}",
      "Register"
    ]
  },
  register: {
    text: [
      "Welcome to register",
      "SMS code sending...",
      "Get SMS code",
      "Registering...",
      "Register now",
      "Have an account,download now",
      "Agree to the terms of the disclaimer",
      "Phone registration",
      "Email registration",
      "Get email code",
      "Email code sending..."
    ],
    placeholder: [
      "Please enter your phone number",
      "Please enter SMS verification code",
      "Please enter the login password",
      "Please confirm your password",
      "Please enter the invitation code",
      "Two passwords are different",
      "Please enter the verification code",
      "Please enter your email",
      "Please tick to agree to the terms of the disclaimer",
      "Please input email verification code"
    ],
    i18n: [
      "Have an account? {a} {line}",
      "Login"
    ]
  },
  footer: [
    "Home",
    "Task",
    "Chat",
    "VIP",
    "My",
    "Profit"
  ],
  home: {
    broadcast: "Congratulations to the member {member}<br> for recommending a {vipname}<br> and getting the {currency} {grade} Promotion Award",
    menu: [
      "VIP area",
      "Video tutorial",
      "Promotion rewards"
    ],
    taskHall: {
      title: [
        "Task hall",
        "Business Hall"
      ]
    },
    memberList: {
      title: "Membership list",
      data: [
        "Congratulations {member}",
        "Complete {num} list today"
      ]
    },
    businessList: {
      title: "Merchant list",
      data: [
        "{member}",
        "Release {num} single task today"
      ]
    },
    noticeTitle: "The latest announcement",
    msg: "The task is not open",
    video: "No video tutorial",
    // Home page new translations
    notification: "Increase product sales to achieve profitability",
    noMoreData: "No more data",
    noTaskData: "No task data",
    defaultTaskTitle: "Task Title",
    commissionTag: "Commission",
    logoText: "Cobwe",
    teamTab: "Team",
    // Process steps
    processSteps: [
      "Join Member",
      "Select Product",
      "Place Order",
      "Get Commission"
    ],
    // Category tabs
    allCategory: "All",
    // Sort button
    priceSort: "Price"
  },
  postTask: {
    navBar: {
      title: "Release mission",
      right: "Release rules"
    },
    field: [{
        label: "Task classification",
      },
      {
        label: "Task title",
        placeholder: "Please enter a task title",
        error: "Task title cannot be empty"
      },
      {
        label: "Task Brief",
        placeholder: "Please enter a task description"
      },
      {
        label: "Task unit price",
        placeholder: "Please enter the task unit price",
        error: "Task unit price cannot be empty"
      },
      {
        label: "Number received",
        placeholder: "Please enter the number",
        error: "The quantity cannot be empty"
      },
      {
        label: "Number of receipts",
        placeholder: "Please enter the number of receipts",
        right: "Times/person",
        error: "The number of receipts cannot be empty"
      },
      {
        label: "Total task price",
        error: "The total task price is not calculated"
      },
      {
        label: "Link information",
        placeholder: "Please enter the link address",
        error: "Link information cannot be empty"
      },
      {
        label: "Task level",
      },
      {
        label: "Deadline",
        placeholder: "Click to select date",
        error: "Deadline cannot be empty"
      },
      {
        label: "Conditions of completion",
        error: "Please select a completion condition"
      },
      {
        label: "Upload requirements",
        placeholder: "Please enter upload request",
      },
      {
        label: "Sample audit",
      },
      {
        label: "Steps",
        placeholder: "Easy to understand steps help to complete!",
        error: "Operation step cannot be empty",
        img: "Step diagram is not perfect"
      },
    ],
    button: "Submit",
    step: {
      title: "Steps",
      left: "Cancel",
      right: "Completed",
      placeholder: "Please enter a step description",
      button: [
        "Delete",
        "Add"
      ]
    },
    tips: [
      "Platform handling fee {pump}{br}This release will require payment of about {price},Please ensure that the balance of the account is sufficient {a}{br}Tip: This platform prohibits the release of pornography, gambling, drugs and all other legal prohibited content, such as deduction points, blocked account.",
      "To recharge",
      "Keep at least one step",
      "Add up to ten steps"
    ]
  },
  vip: {
    user: {
      title: [
        "Your identity",
        "Visitors"
      ],
      label: "Daily Tasks",
      value: [
        "Effective date",
        "Permanent validity",
        "Please log in first"
      ],
    },
    list: {
      label: "Daily Tasks:{number}",
      commission: "Per order",
      button: [
        "Join now",
        "Immediate Renewal"
      ]
    },
    dialog: [
      "Are you sure to spend {currency} {amount} administrative service fee to become {name}?",
      "You are currently {currname} and cannot be {name}",
      "Are you sure to spend {currency} {amount} administrative service fee to renew {name}?"
    ],
    // VIP page new translations
    expireTime: "Expire Time",
    benefitsTitle: "Exclusive Benefits",
    loading: "Loading...",
    loadingDesc: "Getting member level information...",
    exclusivePrivilege: "{name} Exclusive Privilege",
    upgradeCost: "Upgrade Cost",
    memberLevel: "Member Level",
    levelAuth: "Level {level} Member Authentication",
    upgradeNow: "Upgrade now to enjoy exclusive VIP treatment!",
    referralReward: "Referral Commission Reward",
    referralDesc: "For each successful referral of a new user registration, you can get",
    referralPercent: "{percent}% referral commission reward",
    inviteMore: "The more you invite, the richer the rewards!",
    // Button texts
    buttons: {
      payNow: "Pay Now",
      renewNow: "Renew Now",
      buyNow: "Buy Now"
    },
    // User status messages
    userLocked: "Account is locked, unable to purchase VIP",
    vipLocked: "This VIP level is locked and cannot be purchased",
    expired: "Expired",
    expireTime: "Expire Time",
    // Benefits details
    benefits: {
      dailyTasks: "Daily Task Limit",
      dailyTasksDesc: "Complete up to <strong>{number} tasks</strong> per day",
      taskRewards: "Enjoy more task earning opportunities",
      earnMore: "More tasks, more earnings!",
      lotteryTimes: "Daily Lottery Chances",
      lotteryTimesDesc: "Get <strong>{times} lottery chances</strong> per day",
      lotteryChance: "More chances, more winning possibilities",
      lotteryRewards: "Lucky wheel, endless surprises!",
      taskBonus: "Task Income Bonus",
      taskBonusDesc: "Complete tasks to get <strong>{income}x</strong> income bonus<br/>Make every effort you put in get more rewards<br/>Earn efficiently, get rich easily!",
      teamShare: "Team Income Share",
      teamShareDesc: "Enjoy <strong>{income1}x</strong> income share from team members<br/>The stronger the team, the richer your income<br/>Passive income, earn while you sleep!",
      commission: "Referral Commission Reward",
      commissionDesc: "For each successful referral of a new user, get <strong>{percent}%</strong> commission reward",
      commissionMore: "The more you invite, the richer the rewards!"
    }
  },
  user: {
    default: [
      "My superior",
      "Account",
      "Invitation code",
      "Exit login",
      "Balance",
      "Gold",
      "My wallet",
      "Seal",
      "Restriction",
      "Good",
      "Excellent"
    ],
    myEarnings:{
      grid: [
      "Balance",
      "Yesterday's earnings",
      "Today's earnings",
      "This week's earnings",
      "This month's earnings",
      "Last month's earnings",
      "Total revenue",
      "Complete the task today(PCE)",
      "Today's remaining tasks(PCE)",
      "Today's commission",
      "Today's rebate"
    ]
    },
    menu: [
      "Task records",
      "Audit task",
      "Release management",
      "Personal information",
      "Account binding",
      "Daily statement",
      "Accounting records",
      "Invite friends",
      "Team Reports",
      "Helpbook",
      "Credit centres",
      "Download APP",
      "Agent to buy VIP"
    ],
    // 邀请横幅翻译
    inviteBanner: {
      title: "Invite to Earn Coins",
      subtitle: "Click to Invite Friends"
    },
    YUEBAO: ["Yuebao", "Transfer in method", "Storage period", "day", "Revenue per day", "Deposit amount", "revenue", "Please input the deposit amount", "Confirm", "Please select information", "Please select the product first", "Excellent", "Robot", "Yuebao"],
    newlist: ["No record", "revenue", "Amount", "Purchase time", "Status"],
    robot:[
      "Cloud housekeeper",
      "Service description",
      "1. The opening service fee is 99 / month, effective the next day",
      "2. After it comes into effect, it will automatically complete the daily task for the customer, and automatically complete the daily task and settle the income before 8 a.m. every day",
      "Open Cloud housekeeper"
    ],
    // My page specific translations
    myPage: {
      account: "Account",
      inviteCode: "Invite Code",
      logout: "Logout",
      balance: "Balance",
      myWallet: "My Wallet",
      totalEarnings: "Total Earnings",
      todayRemaining: "Today's Earnings",
      luckyDraw: "Lucky Draw",
      inviteFriends: "Invite Friends"
    }
  },
  userInfo: {
    default: [
      "Personal information",
      "Head portrait",
      "Mobile number",
      "Bank card",
      "Alipay",
      "Detailed information",
      "Login password",
      "Fund password",
      "Click Settings",
      "Change head portrait",
      "Change login password",
      "Change fund password",
      "Submit",
      "Empty the cache"
    ],
    label: [
      "Original login password",
      "New login password",
      "Confirming passwords",
      "Original money code",
      "New money code",
      "Confirm password"
    ],
    placeholder: [
      "Please enter the original login password",
      "Please enter a new login password",
      "Please confirm the login password",
      "Please enter the original fund password",
      "Please enter the new funds password",
      "Please confirm the fund password"
    ]
  },
  userSet: {
    default: [
      "Real name certification",
      "Detailed information",
      "Bind Alipay",
      "Submit"
    ],
    label: [
      "Real name",
      "QQ",
      "Alipay account",
      "Alipay Name"
    ],
    placeholder: [
      "Please enter a real name",
      "Enter QQ number",
      "Please enter Alipay account",
      "Please enter Alipay name"
    ]
  },
  bindAccount: {
    default: [
      "Bind Account",
      "View the tutorial",
      "Submit"
    ],
    tabs: [
      "Account",
      "Account",
      "Account"
    ],
    label: [
      "Instructions",
      "Add screenshot",
      "Account"
    ],
    placeholder: "Please enter your {account} account"
  },
  bankCard: {
    default: [
      "Bind bank card",
      "Authenticate now",
      "Submitting ...",
      "Add it now",
      "Add bank card"
    ],
    tips: [
      "Please add your bank card after real name authentication",
      "The account opening name of your bound bank card must be the same as your verified real name, otherwise you will not be able to withdraw successfully."
    ],
    label: [
      "Name",
      "Bank name",
      "Bank account",
      "Bank type",
      "IFSC code",
      "Phone number",
      "Email",
      "Wallet address",
      "Alipay account",
      "WeChat account",
      "Remark"
    ],
    placeholder: [
      "Please select the bank name",
      "Please enter the Bank account",
      "Please select bank type",
      "Please enter the IFSC code",
      "Please enter mobile phone number",
      "Please enter email",
      "Please enter wallet address",
      "Please enter Alipay account",
      "Please enter WeChat account",
      "Please enter remark"
    ]
  },
  wallet: {
    default: [
      "My wallet",
      "Recharge",
      "Withdrawal",
      "Recharge record",
      "Withdrawal record",
      "R",
      "W",
      "Alipay"
    ],
    label: [
      "Withdrawal method",
      "Withdrawal amount",
      "Fund password",
      "Submit",
      "Phone number",
      "Email",
      "IFSC",
      "Withdrawal bank",
      "Withdrawal fee"
    ],
    placeholder: [
      "Select withdrawal method",
      "Please input the withdrawal amount",
      "Please input fund password",
      "Please select the withdrawal method",
      "Please enter the payee's mobile phone number",
      "Please enter the payee's email address",
      "Please enter the payee IFSC",
      "Please select the withdrawal bank"
    ],
    msg: [
      "You haven't set the money code yet, please set it first",
      "You haven't tied a bank card, please bind it first"
    ],
    info: {
      currentBalance: "Current Balance",
      withdrawAmount: "Withdrawal Amount",
      fee: "Fee",
      totalDeduction: "Total Deduction"
    },
    warning: {
      insufficientFunds: "Insufficient Balance",
      shortfall: "Shortfall",
      insufficientBalance: "Insufficient balance! Withdrawal amount {currency}{withdrawAmount}, current balance {currency}{currentBalance}, shortfall {currency}{shortfall}",
      insufficientBalanceWithFee: "Insufficient balance! Withdrawal amount {currency}{withdrawAmount} + fee {currency}{fee} = total {currency}{totalRequired}, current balance {currency}{currentBalance}, shortfall {currency}{shortfall}"
    }
  },
  recharge: {
    default: [
      "Recharge",
      "Recharge details",
      "Recharge record",
      "Available balance {money}, please choose recharge method",
      "Single minimum amount is {currency} {min}, maximum {currency} {max}, handling fee {fee}%",
      "Submitting...",
      "Recharge now",
      "Return",
      "Loading..."
    ],
    info: [
      "Recharge amount",
      "Order number",
      "Receiving bank",
      "Receiving account",
      "Recipient",
      "Payer name",
      "Payer phone number",
      "Payer UPI account",
      "Payer email",
      "Bank name",
      "Account type",
      "Remarks"
    ],
    placeholder: [
      "Please enter recharge amount",
      "Please select recharge channel",
      "Please enter transfer name",
      "Single minimum amount is {currency}{min}",
      "Single maximum amount is {currency}{max}",
      "Please enter payer name",
      "Please enter payer phone number, add international code, e.g. 86",
      "Please enter payer UPI account",
      "Please enter payer email"
    ],
    label: [
      "Recharge amount",
      "Recharge channel",
      "Transfer name",
      "Payer name",
      "Payer phone number",
      "Payer UPI account",
      "Payer email"
    ],
    tips: [
      "Please select the following method to transfer the corresponding amount to avoid delays in financial collection<br>Please upload transfer screenshot as verification proof after transfer",
      "No need to add friends, scan QR code to transfer money to me",
      "Please complete the transfer according to the information below",
      "Tip: After successful payment, please contact online customer service and provide your member account, recharge amount, order number, depositor account, recharge time; to facilitate timely fund addition by finance",
      "Note: Members please submit each transfer payment once",
      "Please upload transfer screenshot as verification proof after transfer",
      "Please complete payment within the specified time",
      "Payment timeout, please resubmit",
      "Payment successful, please wait for processing"
    ]
  },
  task: {
    default: [
      "Task list",
      "Task requirements",
      "Create",
      "Audit",
      "Open the link",
      "Copy the link",
      "Unit Price",
      "Submit",
      "Waiver"
    ],
    tabs: [
      "Doing",
      "Audit",
      "Completed",
      "Failed",
      "Malice",
      "Abandoned"
    ],
    msg: "Please upload the finished picture",
    info: [
      "Task details",
      "Task title",
      "Task benefits",
      "Purchase Amount",
      "Task description",
      "Upload requirements",
      "Submit sample",
      "The user did not submit the sample",
      "Audit notes",
      "Date of audit",
      "Demand side",
      "Release",
      "Copy",
      "Jump",
      "Task steps",
      "Step {index}",
      "Audit sample",
      "No audit sample",
      "Data loading...",
      "Give up the task",
      "Submit completed task",
      "Publish content"
    ],
    index: [
      "Current status",
      "Your current rating is <i style=\"color:#1989fa\">{currVip}</i><br>can only claim the current <i style=\"color:#1989fa\">{currVip}</i> level task<br>do you want to join <i style=\"color:#dd6161\">{vip}</i> level",
      "Join now",
      "Please select task category"
    ],
    list: [
      "Task list",
      "Demand side",
      "Remaining",
      "Request",
      "Receiving"
    ],
    show: [
      "Task details",
      "Man has earned it",
      "Remaining {num} places",
      "Audit within 48 hours",
      "Task description",
      "Demand side",
      "Audit standards",
      "Copy",
      "Jump",
      "Task steps",
      "Step {index}",
      "Audit sample",
      "No audit sample",
      "Data loading...",
      "Submitting...",
      "Collect task",
      "Log in now",
      "Publish content",
      "Product Details",
      "Product Image",
      "Product Title",
      "Cashback",
      "Price",
      "Sold",
      "Buy Now",
      "Purchase Confirmation",
      "Confirm Purchase",
      "Cancel",
      "Are you sure you want to purchase this task?",
      "The corresponding amount will be deducted after purchase",
      "Purchase Successful",
      "Back to Home",
      "Task purchased successfully!",
      "You can check the progress in My Tasks",
      "Purchase Price:",
      "Commission Earned:",
      "Level Required",
      "Insufficient Level",
      "Loading Task Info",
      "Task Already Taken",
      "Task Full",
      "Currently Unavailable",
      "This task has been taken, please choose another task",
      "This task is full, please check other tasks",
      "Please try again later or contact customer service",
      "Click for Details",
      "Level Mismatch, Cannot Purchase",
      "This task requires {requiredLevel} level, your current level does not match",
      "This task requires {requiredLevel} level, you are currently {currentLevel}, level mismatch cannot purchase",
      "Membership Expired",
      "Your membership has expired, please renew before purchasing tasks",
      "Your membership has expired, please renew before purchasing tasks",
      "Renew Now"
    ]
  },
  audit: {
    default: [
      "Audit task",
      "Receive users",
      "Pick up date",
      "Update date",
      "Audit"
    ],
    tabs: [
      "Doing",
      "Audit",
      "Completed",
      "Failed"
    ],
    info: [
      "Audit details",
      "Task title",
      "Task amount",
      "Person completed",
      "Remaining {num} places",
      "Task description",
      "Link information",
      "Audit sample",
      "Collect users",
      "Receive",
      "Completion status",
      "Submit sample",
      "The user did not submit the sample",
      "Update date",
      "Data loading...",
      "Malicious",
      "Retrial",
      "Failure",
      "Success",
      "Audit task",
      "Audit instructions",
      "Please enter audit description",
      "If the submitted task is unqualified, it needs to be re submitted for review",
      "Congratulations on the completion of the task",
      "The submitted task page screenshot error, the task failed",
      "Malicious submission task, task failed"
    ]
  },
  postRecord: [
    "Release management",
    "Release",
    "Total",
    "Completed",
    "Deadline",
    "Audit",
    "Rescission",
    "Editors"
  ],
  dayReport: [
    "Daily statement",
    "Total revenue",
    "My mission",
    "My mission benefits",
    "Tasks completed by subordinates",
    "Subordinate task benefits",
    "PCE",
    "Last 30 days",
    "Quantity",
    "Task",
    "Subordinate",
    "Consumption",
    "Date"
  ],
  fundRecord: {
    default: [
      "Expenditure records",
      "Recharge record",
      "Revenue records",
      "R",
      "I",
      "E"
    ],
    tabs: [
      "Revenue",
      "Expenditure",
      "Recharge"
    ],
    tradeDescription: "Trade Description"
  },
  promote: [
    "Your best friend",
    "Invite you to join the {title}",
    "Referral code",
    "Copy recommendation code ",
    "Copy the invitation link",
    "Save the QR code",
    "Promotional incentives",
    "Save the poster successfully",
    "Failed to save the poster, please try a few more times or save the screenshot",
    "Please screenshot",
    "Save Promotion Poster",
    "Save the promotion poster to your phone<br>If you are unsuccessful, please try a few more times or save the screenshot"
  ],
  // Invite page internationalization
  invite: {
    inviteFriends: "Invite Friends",
    yourInviteCode: "Your Invite Code",
    copyInviteCode: "Copy Invite Code",
    inviteLink: "Invite Link",
    copyLink: "Copy Link",
    share: "Share",
    shareInviteNow: "Share Invite Now",
    qrCode: "QR Code",
    saveSuccess: "Save Successful",
    saveFailed: "Save Failed, Please Try Again",
    copySuccess: "Copy Successful",
    copyFailed: "Copy Failed",
    shareTitle: "Invite Friends to Earn Coins",
    shareDescription: "Join us and earn great rewards together!",
    inviteRewards: "Invite Rewards",
    howToInvite: "How to Invite",
    inviteSteps: {
      step1: "Share invite code or link with friends",
      step2: "Friends register and complete first task",
      step3: "You get generous referral rewards"
    }
  },
  teamReport: {
    default: [
      "Search",
      "Referral Income",
      "Task Commission",
      "Team recharge",
      "Team withdraw",
      "Number of first charge",
      "Number of first push",
      "Team size",
      "New team",
      "PCE",
      "Me",
      "User",
      "Recharge",
      "Withdrawals",
      "Rebate",
      "Commission",
      "Select date",
      "No data",
      "Recharge amount",
      "Recharge Number",
      "Recharge Rebate"
    ],
    tabs: [
      "Team Reports",
      "My team"
    ],
    team: [
      "Level 1 Subordinates",
      "Level 2 Subordinates",
      "Level 3 Subordinates"
    ],
    structure: "Team Structure",
    details: "Subordinate Details",
    loading: "Loading team data",
    noData: "No team data",
    userDefault: "User",
    groupId: "Group ID",
    commission: "Commission",
    unknown: "Unknown",
    // Member detail popup related
    memberDetail: "Member Details",
    basicInfo: "Basic Information",
    earningsInfo: "Earnings Information",
    teamInfo: "Team Information",
    statusInfo: "Status Information",
    username: "Username",
    userId: "User ID",
    phone: "Phone",
    email: "Email",
    regTime: "Registration Time",
    invitor: "Invitor",
    totalCommission: "Total Commission",
    todayCommission: "Today's Commission",
    totalRecharge: "Total Recharge",
    balance: "Balance",
    directCount: "Direct Referrals",
    teamCount: "Team Size",
    status: "Status",
    vipLevel: "VIP Level",
    lastLogin: "Last Login",
    statusActive: "Active",
    statusInactive: "Inactive",
    statusSuspended: "Suspended",
    statusBanned: "Banned",
    statusUnknown: "Unknown Status"
  },
  help: [
    "Helpbook",
    "Not available"
  ],
  credit: [
    "Credit centres",
    "Credit note",
    "Credit records",
    "<p>1.Credit points are evaluated once a week</p><p>2.Initial user credit score:<b>60</b></p><p>3.If it is detected that the user does a task to upload a false image, it will be deducted one day:<b>1</b>Upper limit of points deduction:<b>7</b>integral</p><p>4.If the user does not detect the use of false images to increase<b>1</b>integral</p><p>5.Credit score below<b>50</b>Cash withdrawal will be restricted</p><p>6.Credit score below<b>30</b>Halve the number of tapping tasks</p><p>7.Credit score less than or equal to<b>0</b>score Will be banned</p>",
    "My credit",
    "There is no credit record for the time being"
  ],
  appDown: [
    "App Download",
    "Scan QR code to download app",
    "Save QR code",
    "QR code saved successfully",
    "Failed to save QR code, please try a few more times or save the screenshot"
  ],
  dialog: [
    "Tips",
    "Confirm",
    "Submitting...",
    "Copy succeeded",
    "IOS system version is low and not supported",
    "Registering...",
    "Data loading...",
    "Full Value"
  ],
  lineList: [
    "Line selection",
    "Current line",
    "Line"
  ],
  serviceCenter: [
    "Customer service",
    "Hi, exclusive customer service",
    "Glad to serve you.",
    "Self service",
    "Online customer service",
    "Recharge customer service",
    "Line customer service"
  ],
  buyVip: [
    "Agent to buy VIP",
    "Account recharge",
    "Please enter the recharge account number",
    "Recharge VIP",
    "Please choose to recharge VIP",
    "Are you sure to recharge {grade} for the member {user}?",
    "Submit"
  ],
  Activity: [
    "Like Share Million Dollar Carnival",
    "Page loading..."
  ],
  newLc:[
    "Transfer in method",
    "Account balance",
    "Storage period",
    "day",
    "Revenue per day",
    "Deposit amount",
    "Please input the deposit amount",
    "revenue",
    "Submit",
    "Balance financing",
    "Please fill in the information",
    "Please select the product first",
    "records"
  ],
  newLcList:[
    "Purchase records",
    "No data",
    "Revenue",
    "Amount",
    "Purchase time",
    "State"
  ],
  wheel:[
    "Congratulations on winning {name}. The system has sent the award",
    "Congratulations on winning {name}. Please contact the customer service personnel to receive the award",
    "Keep up your efforts, and the winner will be the next time",
    "Lottery notes",
    "won",
    "Large turntable",
    "Remaining draws: ",
    "Start",
    "Insufficient remaining draws"
  ],
  wheelRecords: {
    title: "Winning Records",
    unknownPrize: "Unknown Prize",
    times: " times",
    prizeTypes: {
      task: "Task Reward",
      money: "Coin Reward",
      other: "Other Reward"
    },
    status: {
      received: "Received",
      pending: "Pending",
      unknown: "Unknown"
    }
  },
  usdt:[
    'Bank address',
    "Bind bank address",
    "Add bank address"
  ],

  // Global Payment Multi-language
  globalPay: {
    title: "Global Payment",
    description: "Select your payment method for secure and convenient recharge",
    selectCountry: "Select Country",
    selectCountryPlaceholder: "Please select a country",
    selectCountrySubtitle: "Choose your country or region",
    selectPaymentMethod: "Select Payment Method",
    selectPaymentSubtitle: "Choose your preferred payment method",
    paymentAmount: "Payment Amount",
    enterAmountSubtitle: "Enter the amount you want to recharge",
    orderNumber: "Order Number",
    paymentStatus: "Payment Status",

    steps: {
      selectCountry: "Select Country",
      selectPayment: "Select Payment",
      enterAmount: "Enter Amount"
    },

    quickAmounts: "Quick Amounts",
    amountRange: "Amount Range",
    orderSummary: "Order Summary",
    country: "Country",
    paymentMethod: "Payment Method",
    amount: "Amount",
    fee: "Fee",
    feeRate: "Fee Rate",
    total: "Total",

    tabs: {
      select: "Select Payment",
      processing: "Processing",
      completed: "Completed"
    },

    countries: {
      ID: "Indonesia",
      IN: "India",
      TH: "Thailand",
      VN: "Vietnam",
      MY: "Malaysia",
      BR: "Brazil",
      CN: "China",
      TW: "Taiwan",
      HK: "Hong Kong",
      PH: "Philippines",
      SG: "Singapore"
    },

    paymentMethods: {
      wallet: "E-Wallet",
      bank: "Bank Transfer",
      card: "Bank Card",
      scan: "QR Code Payment",
      online: "Online Banking",
      upi: "UPI Payment",
      qris: "QRIS Payment",
      ovo: "OVO Wallet",
      dana: "DANA Wallet",
      gopay: "GoPay Wallet",
      shopeepay: "ShopeePay",
      linkaja: "LinkAja",
      paytm: "Paytm",
      phonepe: "PhonePe",
      googlepay: "Google Pay",
      truemoney: "TrueMoney",
      promptpay: "PromptPay",
      momo: "MoMo",
      zalopay: "ZaloPay",
      grabpay: "GrabPay",
      boost: "Boost",
      tng: "Touch 'n Go",
      pix: "PIX",
      boleto: "Boleto"
    },

    status: {
      pending: "Pending",
      processing: "Processing",
      completed: "Completed",
      failed: "Payment Failed",
      cancelled: "Cancelled",
      expired: "Expired"
    },

    messages: {
      loading: "Loading...",
      selectCountryFirst: "Please select a country first",
      selectPaymentMethodFirst: "Please select a payment method first",
      enterAmount: "Please enter payment amount",
      noPaymentMethods: "No payment methods available for this country",
      processing: "Processing...",
      amountTooLow: "Payment amount cannot be lower than {min}",
      amountTooHigh: "Payment amount cannot exceed {max}",
      orderCreated: "Order created successfully",
      paymentSuccess: "Payment successful",
      paymentFailed: "Payment failed",
      networkError: "Network error, please try again",
      loading: "Loading...",
      processing: "Processing...",
      redirecting: "Redirecting to payment page..."
    },

    buttons: {
      payNow: "Pay Now",
      cancel: "Cancel",
      retry: "Retry",
      back: "Back",
      confirm: "Confirm",
      selectCountry: "Select Country",
      selectPayment: "Select Payment Method"
    },

    selectBank: "Select Bank",

    placeholders: {
      searchCountry: "Search Country",
      enterAmount: "Enter Amount"
    },

    tips: {
      selectCountryTip: "Please select your country to display available payment methods",
      paymentMethodTip: "Choose the payment method that suits you best",
      amountTip: "Minimum {min}, Maximum {max} per transaction",
      processingTip: "Payment processing, please do not close the page",
      successTip: "Payment successful, funds will arrive within 5-10 minutes"
    }
  }
}
