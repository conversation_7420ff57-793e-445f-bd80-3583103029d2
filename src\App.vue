<template>
  <div class="Body">
    <router-view v-if="isRouterAlive" />
    <!-- <router-link to="/serviceCenter" id="Service" v-if="showService">
      <img :src="`./static/icon/customer.png`" width="55" />
      {{ $t("common[0]") }}
    </router-link> -->
    <router-link to="/activity" id="Turntable" v-show="showActivity">
      <img :src="`./static/icon/turntable.png`" width="56" />
      {{ $t("common[2]") }}
    </router-link>
    <!-- <div class="MiLineBox" v-if="showMiliao">
      <div class="MiLine" id="MiLine" v-show="minMiliao">
        <a href="javascript:;" class="MiLineFixedBtn" @click="openMiliao">密聊</a>
      </div>
      <div class="CancelMiLine" id="CancelMiLine" v-show="minMiliao">
        <img :src="`./static/miliao/icon/icon-cancel.svg`" height="30">
        {{$t('common[1]')}}
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "App",
  provide() {
    return {
      reloadHtml: this.reloadHtml,
    };
  },
  components: {},
  props: [],
  data() {
    return {
      percentNum: 0,
      showEntrance: false,
      networkState: 1,
      isQuit: false,
      isView: true,
      isRouterAlive: true,
      isNotice: false,
      showMiliao: false,
      minMiliao: false,
      translateX: "0",
      translateY: "-150",
      isCancel: false,
      showService: true,
      showActivity: false,
    };
  },
  computed: {},
  watch: {
    $route(to, from) {
      this.isQuit = false;
      if (to.meta.active == "miliao") {
        this.$dialog.resetDefaultOptions();
        this.$toast.resetDefaultOptions();
        this.showService = false;
      } else {
        this.$dialog.setDefaultOptions({
          className: "SiteDialog",
        });
        this.$toast.setDefaultOptions({
          className: "SiteToast",
        });
        localStorage["BackUrl"] = to.name;
        if (to.meta.active == "serviceCenter") {
          this.showService = false;
        } else {
          this.showService = true;
        }
      }
    },
  },
  created() {
    // 强制设置默认语言为中文（如果用户没有手动设置过）
    let is_self_change = localStorage["is_self_change"] || 0;
    if (!is_self_change) {
      // 如果没有语言设置或者当前不是中文，设置为中文
      const currentLang = localStorage["Language"];
      if (!currentLang || currentLang !== 'cn') {
        this.$SetLanguage('zh-CN');
      }
      this.$Model.GetLanguage();
    }

    // 应用启动时初始化用户信息
    this.initUserInfo();
  },
  mounted() {
    if (this.InitData.setting) {
      document.title = this.InitData.setting.web_title;
    }
    $("body").on("click", "#MiLineBtn", () => {
      this.openMiliao();
    });
    $("body").on("click", "#CloseAlert", () => {
      this.$dialog.close();
    });
    /*APP初始化启动*/
    document.addEventListener("plusready", () => {
      document.addEventListener(
        "pause",
        () => {
          this.isView = false;
          console.log("后台");
        },
        false
      );
      document.addEventListener(
        "resume",
        () => {
          this.isView = true;
          console.log("前台");
        },
        false
      );
      document.addEventListener(
        "newintent",
        () => {
          var args = plus.runtime.arguments;
          if (args) {
            this.$router.push(args);
          }
          console.log(args);
        },
        false
      );
      this.winH = document.body.clientHeight;
      plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
        localStorage["AppVersion"] = wgtinfo.version;
        checkUpdate(wgtinfo.version);
      });
      // document.addEventListener("netchange",() => {
      //   var nt = plus.networkinfo.getCurrentType();
      //   switch (nt) {
      //     case plus.networkinfo.CONNECTION_ETHERNET:
      //     case plus.networkinfo.CONNECTION_WIFI:
      //     case plus.networkinfo.CONNECTION_CELL2G:
      //     case plus.networkinfo.CONNECTION_CELL3G:
      //     case plus.networkinfo.CONNECTION_CELL4G:
      //       console.log("有网络");
      //       this.networkState = 1;
      //       break;
      //     case plus.networkinfo.CONNECTION_UNKNOW:
      //     case plus.networkinfo.CONNECTION_NONE:
      //       console.log("无网络");
      //       this.networkState = 0;
      //       break;
      //     default:
      //       console.log("未知网络");
      //       this.networkState = 2;
      //       break;
      //   }
      // },false);
      if (plus.os.name == "iOS") {
        iosBack();
      }
      plus.key.addEventListener("backbutton", () => {
        appBack();
      });
    });
    let retry = 0;
    var checkUrl = UpdateUrl[0];
    const checkUpdate = (wgtVer) => {
      $.ajax({
        url: checkUrl,
        dataType: "json",
        data: {
          version: wgtVer,
        },
        timeout: 2000,
        success: (data) => {
          if (data.code == 0) {
            this.$Dialog.Confirm(data.code_dec, () => {
              downWgt(data.url);
            });
          }
        },
        error: (xhr, type, errorThrown) => {
          retry += 1;
          if (retry < 2) {
            checkUpdate(wgtVer);
          } else {
            if (checkUrl != UpdateUrl[1]) {
              retry = 0;
              checkUrl = UpdateUrl[1];
              checkUpdate(wgtVer);
            } else {
              console.log("网络错误，无法更新");
            }
          }
        },
      });
    };
    const downWgt = (wgtUrl) => {
      const downToast = this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: this.$t("appMsg[0]"),
      });
      var task = plus.downloader.createDownload(
        wgtUrl,
        {
          filename: "_doc/update/",
        },
        (d, status) => {
          if (status == 200) {
            console.log("下载更新成功：" + d.filename);
            this.$toast.clear();
            this.$Dialog.Confirm(this.$t("appMsg[1]"), () => {
              installWgt(d.filename);
            });
          } else {
            console.log("下载更新失败");
            this.$toast.fail(this.$t("appMsg[2]"));
          }
        }
      );

      task.addEventListener("statechanged", (download, status) => {
        switch (download.state) {
          case 2:
            downToast.message = this.$t("appMsg[3]", {
              num: 0,
            });
          case 3:
            this.percentNum = parseInt(
              (download.downloadedSize / download.totalSize) * 100
            );
            downToast.message = this.$t("appMsg[3]", {
              num: this.percentNum,
            });
            break;
          case 4:
            break;
        }
      });
      task.start();
    };
    const installWgt = (path) => {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: this.$t("appMsg[4]"),
      });
      plus.runtime.install(
        path,
        {},
        () => {
          console.log("安装更新文件成功");
          localStorage.clear();
          this.$toast.success({
            forbidClick: true,
            message: this.$t("appMsg[5]"),
            onClose() {
              plus.runtime.restart();
            },
          });
        },
        (e) => {
          console.log("安装更新文件失败[" + e.code + "]：" + e.message);
          this.$toast.success(this.$t("appMsg[6]"));
        }
      );
    };
    const iosBack = () => {
      var startX = 0;
      var endX = 0;
      document.addEventListener(
        "touchstart",
        (event) => {
          if (event.targetTouches.length == 1) {
            var touch = event.targetTouches[0];
            startX = touch.screenX;
          }
        },
        {
          passive: false,
        }
      );
      document.addEventListener(
        "touchend",
        (event) => {
          if (event.changedTouches.length == 1) {
            var touch = event.changedTouches[0];
            endX = touch.screenX;
            if (startX <= 20 && endX >= 80) {
              appBack();
            }
          }
        },
        {
          passive: false,
        }
      );
    };
    const appBack = () => {
      if (this.$route.name == "home") {
        if (this.isQuit) {
          plus.runtime.quit();
          this.isReconnect = false;
          this.Socket && this.Socket.close();
        } else {
          this.$toast({
            message: this.$t("appMsg[7]"),
            position: "bottom",
          });
          this.isQuit = true;
        }
      } else {
        if ($(".van-nav-bar__left").length) {
          $(".van-nav-bar__left").click();
        } else {
          this.$router.go(-1);
        }
      }
    };
    // 拖动
    var obj = document.getElementById("Service");
    var drag = false;
    var screenHeight = document.documentElement.clientHeight;
    var screenWidth = document.documentElement.clientWidth;
    var pageX = screenWidth;
    obj.addEventListener("touchstart", (ev) => {
      drag = true;
      if (obj.setCapture) {
        obj.setCapture();
      }
      $("#Service").removeClass("move");
    });
    obj.addEventListener("touchmove", (ev) => {
      $("#Service").removeClass("move");
      ev.preventDefault();
      ev = ev.touches ? ev.touches[0] : event;
      if (drag) {
        if (ev.pageY < obj.clientHeight / 2) {
          obj.style.bottom = screenHeight - obj.clientHeight + "px";
        } else if (ev.pageY > screenHeight - 5 - obj.clientHeight / 2) {
          obj.style.bottom = "5px";
        } else {
          obj.style.bottom =
            screenHeight - ev.pageY - obj.clientHeight / 2 + "px";
        }
        if (ev.pageX < obj.clientWidth / 2) {
          obj.style.right = screenWidth - obj.clientWidth + "px";
        } else if (ev.pageX > screenWidth - obj.clientWidth / 2) {
          obj.style.right = "0px";
        } else {
          obj.style.right = screenWidth - ev.pageX - obj.clientWidth / 2 + "px";
        }
        pageX = ev.pageX;
      }
    });
    obj.addEventListener("touchend", (ev) => {
      drag = false;
      $("#Service").addClass("move");
      if (pageX > screenWidth / 2) {
        obj.style.right = 0;
      } else {
        obj.style.right = screenWidth - obj.clientWidth + "px";
      }
    });
    var turntableObj = document.getElementById("Turntable");
    turntableObj.addEventListener("touchstart", (ev) => {
      drag = true;
      if (turntableObj.setCapture) {
        turntableObj.setCapture();
      }
      $("#Turntable").removeClass("move");
    });
    turntableObj.addEventListener("touchmove", (ev) => {
      $("#Turntable").removeClass("move");
      ev.preventDefault();
      ev = ev.touches ? ev.touches[0] : event;
      if (drag) {
        if (ev.pageY < turntableObj.clientHeight / 2) {
          turntableObj.style.bottom =
            screenHeight - turntableObj.clientHeight + "px";
        } else if (
          ev.pageY >
          screenHeight - 5 - turntableObj.clientHeight / 2
        ) {
          turntableObj.style.bottom = "5px";
        } else {
          turntableObj.style.bottom =
            screenHeight - ev.pageY - turntableObj.clientHeight / 2 + "px";
        }
        if (ev.pageX < turntableObj.clientWidth / 2) {
          turntableObj.style.right =
            screenWidth - turntableObj.clientWidth + "px";
        } else if (ev.pageX > screenWidth - turntableObj.clientWidth / 2) {
          turntableObj.style.right = "0px";
        } else {
          turntableObj.style.right =
            screenWidth - ev.pageX - turntableObj.clientWidth / 2 + "px";
        }
        pageX = ev.pageX;
      }
    });
    turntableObj.addEventListener("touchend", (ev) => {
      drag = false;
      $("#Turntable").addClass("move");
      if (pageX > screenWidth / 2) {
        turntableObj.style.right = 0;
      } else {
        turntableObj.style.right =
          screenWidth - turntableObj.clientWidth + "px";
      }
    });
  },
  activated() {},
  destroyed() {},
  methods: {
    reloadHtml() {
      this.isRouterAlive = false;
      this.$nextTick(function() {
        this.isRouterAlive = true;
      });
    },
    dragElement() {
      var obj = document.getElementById("MiLine");
      var drag = false;
      var maxX, maxY, objX, objY, curX, curY;
      obj.addEventListener("touchstart", (ev) => {
        ev = ev.touches ? ev.touches[0] : event;
        maxX = $("#MiLine").outerWidth() - $(window).width();
        maxY = $("#MiLine").outerHeight() - $(window).height();
        objX = Number(this.translateX);
        objY = Number(this.translateY);
        drag = true;
        curX = ev.clientX;
        curY = ev.clientY;
        if (obj.setCapture) {
          obj.setCapture();
        }
      });
      obj.addEventListener("touchmove", (ev) => {
        ev.preventDefault();
        ev = ev.touches ? ev.touches[0] : event;
        if (drag) {
          $("#MiLine").addClass("touchmove");
          this.translateX = objX + ev.clientX - curX;
          this.translateY = objY + ev.clientY - curY;

          this.translateX = this.translateX < maxX ? maxX : this.translateX;
          this.translateX = this.translateX >= 0 ? 0 : this.translateX;
          this.translateY = this.translateY < maxY ? maxY : this.translateY;
          this.translateY = this.translateY >= 0 ? 0 : this.translateY;

          obj.style.transform =
            "translate3d(" +
            this.translateX +
            "px," +
            this.translateY +
            "px,0)";
          obj.style.WebkitTransform =
            "translate3d(" +
            this.translateX +
            "px," +
            this.translateY +
            "px,0)";
          $("#CancelMiLine").addClass("open");
          var posY = Math.abs(this.translateY);
          $("#CancelMiLine").removeClass("on");
          this.isCancel = false;
          var cancelY = $("#CancelMiLine").outerHeight() / 1.5;
          if (posY < cancelY) {
            $("#CancelMiLine").addClass("on");
            this.isCancel = true;
          }
        }
      });
      document.addEventListener("touchend", (ev) => {
        $("#MiLine").removeClass("touchmove");
        $("#CancelMiLine").removeClass("open");
        drag = false;
        if (this.isCancel) {
          this.clearMiliao();
        }
        if (obj.releaseCapture) {
          obj.releaseCapture();
        }
      });
    },
    clearMiliao() {
      this.showMiliao = false;
      this.minMiliao = false;
      this.translateX = "0";
      this.translateY = "-150";
      this.isCancel = false;
      localStorage.removeItem("MiLogin");
    },
    openMiliao() {
      if (this.InitData.setting.chat_url) {
        this.$Util.OpenUrl(this.InitData.setting.chat_url);
      } else {
        if (!localStorage["Token"]) {
          this.clearMiliao();
          this.$router.push("/login");
        } else {
          if (this.showMiliao) {
            this.$router.push({
              name: "miliao",
            });
            this.minMiliao = false;
          } else {
            localStorage.removeItem("Home_Active");
            this.$MiModel.PhoneLogin(
              {
                username: this.UserInfo.username,
                susername: this.UserInfo.susername || "",
              },
              (res) => {
                this.$toast.clear();
                if (res.code == 1) {
                  this.showMiliao = true;
                  this.minMiliao = false;
                  this.$router.push({
                    name: "miliao",
                  });
                  this.$nextTick(() => {
                    this.dragElement();
                    $("#MiLine")
                      .removeClass("open")
                      .css({
                        transform:
                          "translate3d(" +
                          this.translateX +
                          "px," +
                          this.translateY +
                          "px,0)",
                        webkitTransform:
                          "translate3d(" +
                          this.translateX +
                          "px," +
                          this.translateY +
                          "px,0)",
                      });
                  });
                }
              }
            );
          }
        }
      }
    },
    openActivity() {
      if (localStorage["Token"] && localStorage["UserId"]) {
        const uid = localStorage["UserId"];
        this.$Util.OpenUrl(`${this.InitData.setting.activity_url}?id=${uid}`);
      } else {
        this.$router.push("/login");
      }
    },

    // 初始化用户信息
    initUserInfo() {
      const token = localStorage['Token'];
      const currentUserInfo = this.$store.state.UserInfo;

      console.log('=== 应用启动，检查用户登录状态 ===');
      console.log('Token存在:', !!token);
      console.log('当前用户信息存在:', !!currentUserInfo);

      // 只有在有Token且没有用户数据时才获取用户信息
      if (token && !currentUserInfo) {
        console.log('检测到Token但无用户数据，正在获取用户信息...');
        this.$Model.GetUserInfo((data) => {
          console.log('应用启动获取用户信息响应:', data);
          if (data.code === 1) {
            console.log('应用启动用户信息获取成功:', data.info);
          } else {
            console.log('应用启动用户信息获取失败:', data.code_dec);
          }
        });
      } else if (token && currentUserInfo) {
        console.log('Token存在且用户数据已加载，跳过获取用户信息');
      } else {
        // 如果没有Token，确保清除所有用户相关数据
        console.log('未检测到Token，清除用户信息');
        this.$store.dispatch('UpdateUserInfo', '');
        // 清理可能残留的localStorage数据
        localStorage.removeItem('UserInfo');
      }

      console.log('=== 用户信息初始化完成 ===');
    },
  },
};
</script>
<style scoped>
.MiLineBtn {
  position: absolute;
  left: 0;
  z-index: 99;
  color: #fff;
  font-size: 16px;
  line-height: 3em;
  display: block;
  padding: 0 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.MiLineBtn:before {
  content: "\E909";
  font-size: 20px;
  font-family: iconfont;
  vertical-align: middle;
  margin-right: 5px;
}

.MiLine {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 99999;
  border: 6px solid rgba(55, 210, 59, 0.35);
  border-radius: 100%;
  width: 58px;
  height: 58px;
  opacity: 1;
  overflow: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.MiLine.open {
  width: 100%;
  height: 100%;
  border-radius: 0;
  border-width: 0;
  overflow: inherit;
  opacity: 1;
}

.MiLine.touchmove {
  -webkit-transition-duration: 0s;
  transition-duration: 0s;
}

.MiLineFixedBtn {
  font-size: 0;
  display: block;
  width: 100%;
  height: 100%;
  background: url("../static/miliao/icon/icon-miliao.svg") no-repeat;
  background-size: cover;
}

.CancelMiLine {
  position: fixed;
  z-index: 999;
  left: 0;
  right: 0;
  bottom: -100%;
  height: 60px;
  -webkit-transition: bottom 0.2s;
  transition: bottom 0.2s;
  background: linear-gradient(-90deg, transparent, #000);
  background: -webkit-linear-gradient(-90deg, transparent, #000);
  font-size: 14px;
  color: #aaa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CancelMiLine img {
  margin-right: 10px;
}

.CancelMiLine.open {
  bottom: 0;
}

.CancelMiLine.on {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.CancelMiLine.on img {
  height: 40px;
}
</style>
