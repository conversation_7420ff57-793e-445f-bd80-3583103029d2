<template>
  <div class="Site PageBox">
    <!-- <van-nav-bar
      fixed
      :border="false"
      :title="$t('wheelRecords.title')"
      left-arrow
      @click-left="$router.go(-1)"
    /> -->
    <div class="ScrollBox">
      <van-pull-refresh v-model="isRefresh" @refresh="onRefresh">
        <van-list
          v-model="isLoad"
          :finished="isFinished"
          :finished-text="listData.length ? $t('vanPull[0]') : $t('vanPull[1]')"
          @load="onLoad"
          :class="{ Empty: !listData.length }"
        >
          <div
            class="WheelRecordItem"
            :class="{ 'no-prize': item.prize_type === 0 }"
            v-for="(item, index) in listData"
            :key="index"
          >
            <div class="record-content">
              <div class="record-icon" :style="getIconStyle()">
                <img :src="getPrizeIcon(item)" :alt="item.prize_name" />
              </div>
              <div class="record-info">
                <div class="record-header">
                  <span class="prize-name">{{
                    item.wheel_name || $t("wheelRecords.unknownPrize")
                  }}</span>
                  <span class="record-time">{{
                    formatTime(item.time_formatted || item.time)
                  }}</span>
                </div>
                <div class="record-details">
                  <span class="prize-type">{{
                    getPrizeTypeText(item.prize_type)
                  }}</span>
                  <span
                    class="prize-value"
                    v-if="item.prize_value && item.prize_type !== 0"
                  >
                    {{ item.prize_value }}
                  </span>
                </div>
                <div class="record-status" v-if="item.prize_type !== 0">
                  <span :class="getStatusClass(item.is_expired)">
                    {{ getStatusText(item.is_expired) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: "WheelRecords",
  components: {},
  props: [],
  data() {
    return {
      listData: [],
      isLoad: false,
      isRefresh: false,
      isFinished: false,
      pageNo: 0,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.$parent.navBarTitle = this.$t("wheelRecords.title");
    this.getListData("init");
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    onLoad() {
      this.getListData("load");
    },
    onRefresh() {
      this.getListData("init");
    },
    getListData(type) {
      this.isLoad = true;
      this.isRefresh = false;
      if (type == "load") {
        this.pageNo += 1;
      } else {
        this.pageNo = 1;
        this.isFinished = false;
      }

      this.$Model.getWheelWinRecords({ page_no: this.pageNo }, (data) => {
        this.$nextTick(() => {
          this.isLoad = false;
        });
        if (data.code == 1) {
          // 根据新的数据结构处理
          const records =
            (data.data && data.data.records) || data.info || data.data || [];
          const pagination = (data.data && data.data.pagination) || {};

          if (type == "load") {
            if (this.pageNo == 1) {
              this.listData = records;
            } else {
              this.listData = this.listData.concat(records);
            }
          } else {
            this.listData = records;
          }

          // 根据分页信息检查是否还有更多数据
          if (pagination.has_more !== undefined) {
            this.isFinished = !pagination.has_more;
          } else if (
            data.data_total_page &&
            this.pageNo >= data.data_total_page
          ) {
            this.isFinished = true;
          } else if (!records || records.length === 0) {
            this.isFinished = true;
          } else {
            this.isFinished = false;
          }
        } else {
          this.listData = [];
          this.isFinished = true;
          if (data.code_dec) {
            this.$Dialog.Toast(data.code_dec);
          }
        }
      });
    },

    // 获取奖品图标
    getPrizeIcon(item) {
      if (item.prize_icon) {
        return this.InitData.setting.up_url + item.prize_icon;
      }

      // 根据奖品类型返回默认图标
      switch (item.prize_type) {
        case 1: // 任务类型
        case "1":
          return require("@/static/images/number.png");
        case 2: // 金钱类型
        case "2":
          return require("@/static/images/money.png");
        default:
          return require("@/static/images/fonts.png");
      }
    },

    // 获取图标背景样式 - 使用背景图片
    getIconStyle() {
      return {
        backgroundImage: `url(${require('@/static/images/iconbg.png')})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    },

    // 获取奖品类型文本
    getPrizeTypeText(type) {
      switch (type) {
        case 1:
        case "1":
          return this.$t("wheelRecords.prizeTypes.task");
        case 2:
        case "2":
          return this.$t("wheelRecords.prizeTypes.money");
        case 0:
        case "0":
          return "谢谢参与";
        default:
          return this.$t("wheelRecords.prizeTypes.other");
      }
    },

    // 格式化奖品价值
    formatPrizeValue(item) {
      if (item.prize_type == 2 || item.prize_type == "2") {
        // 金钱类型显示货币符号
        return `${this.$Currency.getSymbol()}${Number(
          item.prize_value
        ).toLocaleString()}`;
      } else if (item.prize_type == 1 || item.prize_type == "1") {
        // 任务类型显示次数
        return `${item.prize_value}${this.$t("wheelRecords.times")}`;
      }
      return item.prize_value;
    },

    // 获取状态样式类
    getStatusClass(isExpired) {
      if (isExpired === true) {
        return "status-default"; // 已过期
      } else if (isExpired === false) {
        return "status-success"; // 有效期内
      } else {
        return "status-pending"; // 待处理
      }
    },

    // 获取状态文本
    getStatusText(isExpired) {
      if (isExpired === true) {
        return "已过期";
      } else if (isExpired === false) {
        return "有效";
      } else {
        return "待处理";
      }
    },

    // 格式化时间
    formatTime(timeInput) {
      if (!timeInput) return "--";

      try {
        let date;

        // 如果是时间戳（数字）
        if (typeof timeInput === "number") {
          date = new Date(timeInput * 1000); // 转换为毫秒
        }
        // 如果是格式化的时间字符串
        else if (typeof timeInput === "string") {
          date = new Date(timeInput);
        } else {
          return "--";
        }

        const now = new Date();
        const diff = now - date;

        // 小于1分钟
        if (diff < 60000) {
          return "刚刚";
        }
        // 小于1小时
        else if (diff < 3600000) {
          return Math.floor(diff / 60000) + "分钟前";
        }
        // 小于1天
        else if (diff < 86400000) {
          return Math.floor(diff / 3600000) + "小时前";
        }
        // 小于7天
        else if (diff < 604800000) {
          return Math.floor(diff / 86400000) + "天前";
        }
        // 超过7天显示具体日期
        else {
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          return `${month}-${day}`;
        }
      } catch (error) {
        console.error("时间格式化错误:", error);
        return timeInput;
      }
    },
  },
};
</script>

<style scoped>
.Site {
  background: #f5f5f5;
  min-height: 100vh;
}

.ScrollBox {
  min-height: calc(100vh - 46px);
  padding-bottom: 20px;
}

.WheelRecordItem {
  margin: 8px 16px;
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.WheelRecordItem:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 谢谢参与样式 */
.WheelRecordItem.no-prize {
  background: #ffffff;
}

.WheelRecordItem.no-prize .record-icon {
  background: linear-gradient(135deg, #8e8e93 0%, #636366 100%);
  box-shadow: 0 4px 12px rgba(142, 142, 147, 0.2);
}

.WheelRecordItem.no-prize .prize-name {
  color: #1a1a1a;
}

.record-content {
  display: flex;
  align-items: flex-start;
}

.record-info {
  flex: 1;
  min-width: 0;
}

.record-icon {
  width: 52px;
  height: 52px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 背景和阴影通过动态样式设置 */
}

.record-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  /* 移除白色滤镜，让图标保持原色 */
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.prize-name {
  font-size: 17px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
  max-width: 200px;
}

.record-time {
  font-size: 12px;
  color: #8e8e93;
  white-space: nowrap;
  margin-left: 8px;
}

.record-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prize-type {
  font-size: 13px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.prize-value {
  font-size: 15px;
  font-weight: 700;
  color: #ff6b35;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.record-status {
  text-align: right;
}

.status-success {
  color: #34c759;
  font-size: 13px;
  font-weight: 600;
  background: #e8f5e8;
  padding: 4px 8px;
  border-radius: 6px;
}

.status-pending {
  color: #ff9500;
  font-size: 13px;
  font-weight: 600;
  background: #fff4e6;
  padding: 4px 8px;
  border-radius: 6px;
}

.status-default {
  color: #8e8e93;
  font-size: 13px;
  font-weight: 500;
  background: #f2f2f7;
  padding: 4px 8px;
  border-radius: 6px;
}

/* 下拉刷新组件样式 */
.van-pull-refresh {
  min-height: calc(100vh - 46px);
}

/* 空状态样式 */
.van-list.Empty {
  min-height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8e8e93;
}

.van-list.Empty::before {
  content: "🎁";
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 导航栏样式优化 */
.van-nav-bar {
  background: #ffffff !important;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.van-nav-bar__title {
  color: #1a1a1a !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

/* 强制覆盖全局样式 - 使用更高优先级 */
.Site .van-nav-bar .van-nav-bar__title,
.PageBox .van-nav-bar .van-nav-bar__title,
.van-nav-bar .van-nav-bar__title {
  color: #1a1a1a !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

/* 下拉刷新和加载样式 */
.van-pull-refresh {
  background: #f5f5f5;
}

.van-list__finished-text {
  color: #8e8e93;
  font-size: 14px;
  padding: 20px 0;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .WheelRecordItem {
    margin: 6px 12px;
    padding: 14px;
  }

  .record-icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .prize-name {
    font-size: 16px;
    max-width: 160px;
  }
}
</style>
