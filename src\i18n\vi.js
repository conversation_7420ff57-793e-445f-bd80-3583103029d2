export default {
  line: "Chuyển tuyến",
  language: "Chọn ngôn ngữ",
  signin: "kí vào",
  baozhengjin: "保证金",
  unbaozhengjin: "解冻保证金",
  common: [
    "Chăm sóc khách hàng trực tuyến",
    "Hủy tính năng đa cửa sổ",
    "Vẽ may mắn",
    "Thông báo",
    "Cài đặt",
    "Tr<PERSON> giúp",
    "<PERSON><PERSON> chúng tôi",
    "Xác nhận"
  ],
  footer: ["Trang đầu", "Nhiệm vụ", "", "VIP", "Của tôi", "<PERSON>anh thu"],
  home: {
    broadcast:
      "Chúc mừng thành viên {member}<br>đề xuất một người{vipname}<br>nhận được {currency}{grade}tiền thưởng quảng bá!",
    taskHall: {
      title: ["Sảnh nhiệm vụ", "<PERSON><PERSON><PERSON> thương gia đăng"],
    },
    memberList: {
      title: "<PERSON><PERSON> sách thành viên",
      data: [
        "Chú<PERSON> mừng :{member}",
        "hôm nay đã hoàn thành {num}nhiệm vụ ,nhận được {currency}{profit}!"
      ],
    },
    businessList: {
      title: "Danh sách thương gia ",
      data: ["{member}", "Hôm nay đăng  {num}nhiệm vụ"],
    },
    noticeTitle: "Lời nhắc nhở",
    menu: ["Chuyên VIP", "Video hướng dẫn", "Tiền thưởng quảng bá"],
    msg: "Nhiệm vụ chưa mở",
    video: "Tạm thời không có video hướng dẫn",
    // Trang chủ bản dịch mới
    notification: "Tăng doanh số bán hàng để đạt được lợi nhuận",
    noMoreData: "Không có dữ liệu nào nữa",
    noTaskData: "Không có dữ liệu nhiệm vụ",
    defaultTaskTitle: "Tiêu đề nhiệm vụ",
    commissionTag: "Hoa hồng",
    logoText: "Cobwe",
    teamTab: "Đội nhóm",
    // Các bước quy trình
    processSteps: [
      "Tham gia thành viên",
      "Chọn sản phẩm",
      "Đặt hàng",
      "Nhận hoa hồng"
    ],
    // Tab danh mục
    allCategory: "Tất cả",
    // Nút sắp xếp
    priceSort: "Giá"
  },
  login: {
    text: [
      "Ghi nhớ tên đăng nhập/mật khẩu",
      "Đang đăng nhập...",
      "Đăng nhập ngay",
      "Đăng nhập"
    ],
    placeholder: [
      "Nhập số điện thoại hay email",
      "Vui lòng nhập mật khẩu đăng nhập"
    ],
    i18n: ["Không có {title}tài khoản ？{a} {line}", "Đăng kí "],
  },
  register: {
    text: [
      "Hoan nghênh đăng ký",
      "Đang gửi mã xác minh qua tin nhắn ...",
      "Nhận mã xác minh qua tin nhắn",
      "Đang đăng kí...",
      "Đăng kí ngay",
      "Đã có tài khoản，tải ngay",
      "Thỏa thuận không trách nhiệm",
      "Số điện thoại",
      "Đăng kí qua email",
      "Nhận mã email",
      "Gởi mã thư..."
    ],
    placeholder: [
      "Vui lòng nhập số điện thoại",
      "Vui lòng nhập mã xác minh qua tin nhắn ",
      "Vui lòng nhập mật khẩu đăng nhập",
      "Vui lòng xác nhận mật khẩu của bạn",
      "Vui lòng nhập mã mời",
      "Hai lần nhập mật khẩu không khớp",
      "Vui lòng nhập mã xác minh ",
      "Vui lòng nhập địa chỉ email",
      "Xin kiểm tra miễn trách nhiệm hợp đồng",
      "Nhập mật mã kiểm tra email"
    ],
    i18n: ["Đã có tài khoản？{a} {line}", "Đăng nhập"],
  },
  postTask: {
    navBar: {
      title: "Nhiệm vụ đăng",
      right: "Quy tắc đăng",
    },
    field: [
      {
        label: "Phân loại nhiệm vụ",
      },
      {
        label: "Tiêu đề nhiệm vụ",
        placeholder: "Vui lòng nhập tiêu đề nhiệm vụ",
        error: "Tiêu đề nhiệm vụ không được để trống",
      },
      {
        label: "Giới thiệu sơ lược về nhiệm vụ",
        placeholder: "Vui lòng nhập giới thiệu về nhiệm vụ",
      },
      {
        label: "Đơn giá nhiệm vụ",
        placeholder: "Vui lòng nhập đơn giá nhiệm vụ",
        right: "{currency}",
        error: [
          "Đơn giá nhiệm vụ không được để trống",
          "Đơn giá nhiệm vụ không được thấp hơn 1{currency}"
        ],
      },
      {
        label: "Số lượng nhận ",
        placeholder: "Vui lòng nhập số lượng nhận",
        error: "Số lượng nhận không được để trống",
      },
      {
        label: "Số lần nhận",
        placeholder: "Vui lòng nhập số lần nhận",
        right: "lần /người ",
        error: "Số lần nhận không được để trống",
      },
      {
        label: "Tổng giá nhiệm vụ",
        error: "Chưa tính tổng giá nhiệm vụ",
      },
      {
        label: "Thông tin liên kết",
        placeholder: "Vui lòng nhập địa chỉ liên kết",
        error: "Thông tin liên kết không được để trống",
      },
      {
        label: "Cấp bậc nhiệm vụ",
      },
      {
        label: "Ngày kết thúc",
        placeholder: "Nhấn để chọn ngày tháng",
        error: "Ngày kết thúc không được để trống",
      },
      {
        label: "Điều kiện hoàn thành",
        error: "Vui lòng chọn điều kiện hoàn thành",
      },
      {
        label: "Gửi yêu cầu",
        placeholder: "Nhập yêu cầu tải lên",
      },
      {
        label: "Ví dụ về xét duyệt",
      },
      {
        label: "Các bước thao tác",
        placeholder: "Các bước đơn giản dễ hiểu có lợi cho việc hoàn thành !",
        error: "Các bước thao tác không được để trống",
        img: "Bước sơ đồ không hoàn hảo",
      },
    ],
    button: "Gửi đi",
    step: {
      title: "Các bước thao tác",
      left: "hủy",
      right: "Hoàn thành ",
      placeholder: "Vui lòng nhập mô tả các bước",
      button: ["Xóa", "Thêm"],
    },
    tips: [
      "Phí thủ tục của nền tảng khoảng {pump}{br}Lần đăng này cần thanh toán khoảng  {price}，Vui lòng đảm bảo số dư đủ {a}{br}Lời nhắc nhở: Nền tảng này cấm đăng những nội dung khiêu dâm,cá cược,chất gây nghiện,nội dung liên quan chính trị và những nội dung pháp luật cấm，nếu phát hiện sẽ bị trừ điểm，khóa tài khoản",
      "Nạp tiền",
      "Tối thiểu phải giữ lại một bước",
      "Tối đa chỉ thêm mười bước"
    ],
  },
  vip: {
    user: {
      title: ["Vai trò của bạn", "Khách viếng thăm"],
      label: "Nhiệm vụ mỗi ngày",
      value: [
        "Thời gian có hiệu lực",
        "Có hiệu lực vĩnh viễn",
        "Vui lòng đăng nhập trước"
      ],
    },
    list: {
      label: "Nhiệm vụ mỗi ngày:{number}次",
      commission: "Mỗi nhiệm vụ",
      button: ["Gia nhập ngay", "Tiếp tục mua"],
      text: [
        "Doanh thu mỗi ngày",
        "Doanh thu tháng ",
        "Tiền thưởng đề xuất:mỗi người",
        "Miễn phí",
        "Mỗi nhiệm vụ"
      ],
    },
    dialog: [
      "Xác định tốn phí {amount} {currency}trở thành {name}hay không？",
      "Hiện tại bạn là {currname}，Không thể trở thành{name}",
      "Xác định tốn phí {amount} {currency}tiếp tục trả phí {name}hay không？"
    ],
    // VIP trang mới dịch
    expireTime: "Thời gian hết hạn",
    benefitsTitle: "Đặc quyền độc quyền",
    loading: "Đang tải...",
    loadingDesc: "Đang lấy thông tin cấp thành viên...",
    exclusivePrivilege: "Đặc quyền độc quyền {name}",
    upgradeCost: "Chi phí nâng cấp",
    memberLevel: "Cấp thành viên",
    levelAuth: "Xác thực thành viên cấp {level}",
    upgradeNow: "Nâng cấp ngay để tận hưởng đãi ngộ VIP độc quyền!",
    referralReward: "Phần thưởng hoa hồng giới thiệu",
    referralDesc: "Mỗi lần giới thiệu thành công một người dùng mới đăng ký, bạn có thể nhận được",
    referralPercent: "{percent}% phần thưởng hoa hồng giới thiệu",
    inviteMore: "Càng mời nhiều, phần thưởng càng phong phú!",
    // Thông điệp trạng thái
    vipLocked: "Cấp VIP này đã bị khóa, không thể mua",
    expired: "Đã hết hạn",
    expireTime: "Thời gian hết hạn",
    // Văn bản nút
    buttons: {
      payNow: "Thanh toán ngay",
      renewNow: "Gia hạn ngay",
      buyNow: "Mua ngay"
    },
    // Chi tiết quyền lợi
    benefits: {
      dailyTasks: "Số lần nhiệm vụ hàng ngày",
      dailyTasksDesc: "Có thể hoàn thành <strong>{number} nhiệm vụ</strong> mỗi ngày",
      taskRewards: "Tận hưởng nhiều cơ hội thu nhập từ nhiệm vụ hơn",
      earnMore: "Nhiều nhiệm vụ hơn, thu nhập phong phú hơn!",
      lotteryTimes: "Số lần quay số hàng ngày",
      lotteryTimesDesc: "Nhận <strong>{times} cơ hội</strong> quay số mỗi ngày",
      lotteryChance: "Nhiều cơ hội quay số hơn, nhiều khả năng trúng thưởng hơn",
      lotteryRewards: "Vòng quay may mắn, bất ngờ liên tục!",
      taskBonus: "Thưởng thu nhập nhiệm vụ",
      taskBonusDesc: "Hoàn thành nhiệm vụ để nhận <strong>{income} lần</strong> thưởng thu nhập<br/>Biến mọi nỗ lực của bạn thành nhiều phần thưởng hơn<br/>Kiếm tiền hiệu quả, làm giàu dễ dàng!",
      teamShare: "Chia sẻ thu nhập nhóm",
      teamShareDesc: "Hưởng <strong>{income1} lần</strong> chia sẻ thu nhập từ thành viên nhóm<br/>Nhóm càng mạnh, thu nhập của bạn càng phong phú<br/>Thu nhập thụ động, kiếm tiền khi ngủ!"
    }
  },
  user: {
    default: [
      "Cấp trên của tôi",
      "Tài khoản đăng nhập",
      "Mã mời",
      "Đăng xuất",
      "Số dư",
      "Xu vàng",
      "Ví của tôi",
      "Bị khóa",
      "Hạn chế",
      "Tốt",
      "Xuất sắc"
    ],
    myEarnings: {
      grid: [
        "Số dư cá nhân",
        "Doanh thu hôm qua",
        "Doanh thu hôm nay",
        "Doanh thu tuần này",
        "Doanh thu tháng này",
        "Doanh thu tháng trước",
        "Tổng doanh thu",
        "Hôm nay hoàn thành ( )nhiệm vụ ",
        "Hôm nay còn dư ( )nhiệm vụ ",
        "Hoa hồng hôm nay",
        "Hoàn tiền hôm nay"
      ],
    },
    default: [
      "Cấp trên của tôi",
      "Tài khoản đăng nhập",
      "Mã mời",
      "Đăng xuất",
      "Số dư",
      "Tiền xu",
      "Ví của tôi",
      "Khóa tài khoản",
      "Hạn chế",
      "Tốt",
      "Xuất sắc"
    ],
    YUEBAO: [
      "Yuebao",
      "Phương thức chuyển tiền",
      "Giai đoạn lưu trữ",
      "ngày",
      "Thu nhập mỗi ngày",
      "Số tiền được Lưu trữ",
      "Thu nhập Ước tính",
      "Vui lòng nhập số lượng bộ nhớ",
      "mục đích",
      "Vui lòng chọn thông tin",
      "Vui lòng chọn sản phẩm trước",
      "thông minh",
      "người máy",
      "Yuebao"
    ],
    newlist: [
      "Không có hồ sơ",
      "Thu nhập Ước tính",
      "Số tiền",
      "Thời gian mua hàng",
      "trạng thái"
    ],
    menu: [
      "Lịch sử nhiệm vụ",
      "Xét duyệt nhiệm vụ",
      "Quản lý đăng",
      "Thông tin cá nhân",
      "Liên kết tài khoản",
      "Bảng thanh toán theo ngày",
      "Lịch sử thay đổi sổ sách",
      "Mời bạn bè",
      "Bảng nhóm ",
      "Sổ tay hướng dẫn",
      "Trung tâm tín dụng",
      "Tải APP",
      "Thanh toán VIP thay mặt"
    ],
    robot: [
      "Quản gia Vân",
      "Mô tả dịch vụ",
      "1. Phí dịch vụ mở màn là 99/ tháng, hiệu quả ngày hôm sau",
      "2. Sau khi nó được thực hiện, nó sẽ tự động hoàn thành công việc hàng ngày cho khách hàng, và tự động hoàn thành công việc hàng ngày và thu nhập trước 8.m. m ỗi ngày.",
      "Quản gia Mây mở."
    ],
    // Bản dịch banner mời
    inviteBanner: {
      title: "Mời để Kiếm Xu",
      subtitle: "Nhấp để Mời Bạn Bè"
    },
    YUEBAO: ["Yuebao", "Phương thức chuyển tiền", "Giai đoạn lưu trữ", "ngày", "Thu nhập mỗi ngày", "Số tiền được Lưu trữ", "Thu nhập Ước tính", "Vui lòng nhập số lượng bộ nhớ", "mục đích", "Vui lòng chọn thông tin", "Vui lòng chọn sản phẩm trước", "thông minh", "người máy", "Yuebao"],
    newlist: ["Không có hồ sơ", "Thu nhập Ước tính", "Số tiền", "Thời gian mua hàng", "trạng thái"],
    // Bản dịch chuyên dụng trang của tôi
    myPage: {
      account: "Tài khoản",
      inviteCode: "Mã mời",
      logout: "Đăng xuất",
      balance: "Số dư",
      myWallet: "Ví của tôi",
      totalEarnings: "Tổng thu nhập",
      todayRemaining: "Thu nhập hôm nay",
      luckyDraw: "Rút thăm may mắn",
      inviteFriends: "Mời bạn bè"
    }
  },
  userInfo: {
    default: [
      "Thông tin cá nhân",
      "Ảnh đại diện",
      "Số điện thoại",
      "Thẻ ngân hàng",
      "Alipay",
      "Thông tin chi tiết",
      "Mật khẩu đăng nhập",
      "Mật khẩu thanh toán",
      "Nhấn để cài đặt",
      "Đổi ảnh đại diện",
      "Đổi mật khẩu đăng nhập",
      "Đổi mật khẩu thanh toán",
      "Gửi đi",
      "Xóa bộ nhớ cache"
    ],
    label: [
      "Mật khẩu đăng nhập cũ ",
      "Mật khẩu đăng nhập mới ",
      "Xác nhận mật khẩu",
      "Mật khẩu thanh toán cũ",
      "Mật khẩu thanh toán mới",
      "Xác nhận mật khẩu"
    ],
    placeholder: [
      "Vui lòng nhập mật khẩu đăng nhập cũ",
      "Vui lòng nhập mật khẩu đăng nhập mới",
      "Vui lòng xác nhận mật khẩu đăng nhập",
      "Vui lòng nhập mật khẩu thanh toán cũ",
      "Vui lòng nhập mật khẩu thanh toán mới",
      "Vui lòng xác nhận mật khẩu thanh toán "
    ],
  },
  bankCard: {
    default: [
      "Liên kết thẻ ngân hàng",
      "Xác minh ngay",
      "Đang gửi đi...",
      "Thêm ngay",
      "Thêm thẻ ngân hàng"
    ],
    tips: [
      "Vui lòng xác minh danh tính trước rồi mới thêm thẻ ngân hàng",
      "Tên chủ thẻ ngân hàng mà bạn liên kết phải khớp với họ tên thật mà bạn xác minh ,nếu không sẽ không thể rút tiền thành công "
    ],
    label: [
      "Họ tên",
      "Tên ngân hàng",
      "Số tài khoản ngân hàng",
      "Loại ngân hàng",
      "Mã IFSC",
      "Số điện thoại",
      "Hộp thư",
      "Địa chỉ ví"
    ],
    placeholder: [
      "Hãy chọn tên ngân hàng",
      "Vui lòng chọn số tài khoản ngân hàng",
      "Vui lòng chọn loại ngân hàng",
      "Vui lòng nhập mã IFSC",
      "Nhập số điện thoại di động",
      "Nhập email",
      "Vui lòng nhập địa chỉ ví"
    ],
  },
  userSet: {
    default: [
      "Xác minh danh tính",
      "Thông tin chi tiết",
      "Liên kết Alipay",
      "Gửi đi"
    ],
    label: ["Họ tên thật", "Số QQ", "Tài khoản Alipay", "Họ tên trên Alipay"],
    placeholder: [
      "Vui lòng nhập họ tên thật (Họ tên thật dùng để rút tiền)",
      "Vui lòng nhập số QQ",
      "Vui lòng nhập tài khoản Alipay",
      "Vui lòng nhập họ tên trên Alipay"
    ],
    tips:
      "Lời nhắc nhở：Liên kết thẻ ngân hàng không thể thay đổi，sử dụng để rút tiền cho bạn",
  },
  bindAccount: {
    default: ["Liên kết tài khoản", "Hướng dẫn tra cứu", "Gửi đi"],
    tabs: ["tài khoản", "tài khoản", "tài khoản"],
    label: ["Hướng dẫn thao tác", "Thêm ảnh chụp màn hình", "tài khoản"],
    placeholder: "Vui lòng nhập {account}tài khoản",
  },
  dayReport: [
    "Bảng thanh toán theo ngày",
    "Tổng doanh thu",
    "Nhiệm vụ tôi đã hoàn thành",
    "Doanh thu nhiệm vụ của tôi",
    "Nhiệm vụ cấp dưới hoàn thành",
    "Doanh thu nhiệm vụ của cấp dưới",
    "Nhiệm vụ",
    "30 ngày gần nhất",
    "Số lượng",
    "Nhiệm vụ",
    "Cấp dưới",
    "Chi tiêu",
    "Ngày tháng"
  ],
  fundRecord: {
    default: [
      "Lịch sử chi ra",
      "Lịch sử nạp tiền",
      "Lịch sử thu nhập",
      "Nạp",
      "Thu",
      "Chi"
    ],
    tabs: ["Thu nhập", "Chi ra", "Nạp tiền"],
    tradeDescription: "Mô Tả Giao Dịch"
  },
  vanPull: ["Không còn dữ liệu khác", "Không có dữ liệu"],
  promote: [
    "Bạn bè của tôi",
    "Mời bạn gia nhập{title}",
    "Mã đề xuất",
    "Sao chép mã đề xuất",
    "Sao chép liên kết mời",
    "Lưu mã QR",
    "Tiền thưởng quảng bá",
    "Lưu áp phích thành công",
    "Lưu áp phích không thành công，vui lòng thử lại vài lần hoặc chụp lại màn hình để lưu",
    "Vui lòng chụp màn hình",
    "Lưu áp phích quảng bá",
    "Lưu áp phích quảng bá về điện thoại <br>nếu không thành công ，vui lòng thử lại vài lần hoặc chụp lại màn hình để lưu"
  ],
  // Quốc tế hóa trang mời
  invite: {
    inviteFriends: "Mời Bạn Bè",
    yourInviteCode: "Mã Mời Của Bạn",
    copyInviteCode: "Sao Chép Mã Mời",
    inviteLink: "Liên Kết Mời",
    copyLink: "Sao Chép Liên Kết",
    share: "Chia Sẻ",
    shareInviteNow: "Chia Sẻ Lời Mời Ngay",
    qrCode: "Mã QR",
    saveSuccess: "Lưu Thành Công",
    saveFailed: "Lưu Thất Bại, Vui Lòng Thử Lại",
    copySuccess: "Sao Chép Thành Công",
    copyFailed: "Sao Chép Thất Bại",
    shareTitle: "Mời Bạn Bè Để Kiếm Xu",
    shareDescription: "Hãy tham gia cùng chúng tôi và kiếm được những phần thưởng tuyệt vời!",
    inviteRewards: "Phần Thưởng Mời",
    howToInvite: "Cách Mời",
    inviteSteps: {
      step1: "Chia sẻ mã hoặc liên kết mời với bạn bè",
      step2: "Bạn bè đăng ký và hoàn thành nhiệm vụ đầu tiên",
      step3: "Bạn nhận được phần thưởng giới thiệu hậu hĩnh"
    }
  },
  teamReport: {
    default: [
      "Tìm kiếm ",
      "Thu nhập giới thiệu",
      "Hoa hồng nhiệm vụ",
      "Nạp tiền của nhóm",
      "Rút tiền của nhóm",
      "Số người nạp lần đầu",
      "Số người đề xuất lần đầu",
      "Số thành viên của nhóm",
      "Số thành viên tăng thêm của nhóm",
      "người ",
      "Tôi",
      "Người dùng",
      "Nạp tiền",
      "Rút tiền",
      "Chiết khấu",
      "Hoa hồng",
      "Chọn ngày tháng năm",
      "Không tìm thấy dữ liệu",
      "Số tiền nạp",
      "Số người nạp",
      "Hoa hồng nạp"
    ],
    tabs: ["Bảng nhóm", "Nhóm của tôi"],
    team: ["Cấp dưới 1", "Cấp dưới 2", "Cấp dưới 3"],
    structure: "Cấu trúc nhóm",
    details: "Chi tiết cấp dưới",
    loading: "Đang tải dữ liệu nhóm",
    noData: "Không có dữ liệu nhóm",
    userDefault: "Người dùng",
    groupId: "ID Nhóm",
    commission: "Hoa hồng",
    unknown: "Không rõ",
    // Chi tiết thành viên popup
    memberDetail: "Chi tiết thành viên",
    basicInfo: "Thông tin cơ bản",
    earningsInfo: "Thông tin thu nhập",
    teamInfo: "Thông tin nhóm",
    statusInfo: "Thông tin trạng thái",
    username: "Tên người dùng",
    userId: "ID người dùng",
    phone: "Điện thoại",
    email: "Email",
    regTime: "Thời gian đăng ký",
    invitor: "Người mời",
    totalCommission: "Tổng hoa hồng",
    todayCommission: "Hoa hồng hôm nay",
    totalRecharge: "Tổng nạp tiền",
    balance: "Số dư",
    directCount: "Giới thiệu trực tiếp",
    teamCount: "Kích thước nhóm",
    status: "Trạng thái",
    vipLevel: "Cấp VIP",
    lastLogin: "Đăng nhập cuối",
    statusActive: "Hoạt động",
    statusInactive: "Không hoạt động",
    statusSuspended: "Tạm ngưng",
    statusBanned: "Bị cấm",
    statusUnknown: "Trạng thái không rõ"
  },
  help: ["Sổ tay hướng dẫn", "Không tìm thấy nội dung"],
  credit: [
    "Trung tâm tín dụng",
    "Mô tả tín dụng",
    "Lịch sử tín dụng",
    "<p>1.Điểm tín dụng mỗi tuần đánh giá một lần </p><p>2.Điểm tín dụng ban đầu của người dùng :<b>60</b></p><p>3.Nếu phát hiện người dùng đăng tải ảnh làm nhiệm vụ gian lận thì mỗi ngày trừ :<b>1</b>điểm ,số điểm trừ tối đa :<b>7</b>điểm </p><p>4.Nếu không phát hiện người dùng đăng tải ảnh làm nhiệm vụ gian lận thì mỗi ngày tăng thêm <b>1</b>điểm</p><p>5.Điểm tín dụng thấp hơn <b>50</b>điểm thì sẽ bị hạn chế rút tiền </p><p>6.Điểm tín dụng thấp hơn <b>30</b>điểm thì giảm nửa số lần nhận nhiệm vụ</p><p>7.Điểm tín dụng ít hơn hoặc bằng <b>0</b>điểm thì sẽ bị khóa tài khoản</p>",
    "Tín dụng của tôi",
    "Tạm thời không có lịch sử tín dụng"
  ],
  upload: [
    "Đang đăng tải...",
    "Lỗi định dạng",
    "Đăng tải thành công",
    "Đăng tải không thành công"
  ],
  task: {
    default: [
      "Bảng nhiệm vụ",
      "Yêu cầu nhiệm vụ",
      "Tạo",
      "Xét duyệt",
      "Mở liên kết",
      "Sao chép liên kết",
      "Đơn giá",
      "Gửi đi",
      "Từ bỏ"
    ],
    tabs: [
      "Đang tiến hành",
      "Đang xét duyệt",
      "Đã hoàn thành",
      "Đã thất bại",
      "Cố ý",
      "Đã từ bỏ"
    ],
    msg: "Vui lòng đăng ảnh hoàn thành nhiệm vụ",
    info: [
      "Chi tiết nhiệm vụ",
      "Tiêu đề nhiệm vụ",
      "Doanh thu nhiệm vụ",
      "Số tiền mua",
      "Mô tả nhiệm vụ",
      "Yêu cầu đăng",
      "Mẫu gửi đi",
      "Mẫu người dùng chưa gửi đi",
      "Mô tả xét duyệt",
      "Ngày xét duyệt",
      "Bên có nhu cầu",
      "Đăng",
      "Sao chép",
      "Chuyển qua",
      "Các bước nhiệm vụ",
      "Bước thứ {index}",
      "Mẫu xét duyệt",
      "Không có mẫu xét duyệt",
      "Đang tại dữ liệu...",
      "Từ bỏ nhiệm vụ",
      "Gửi nhiệm vụ đã hoàn thành",
      "Đăng tải nội dung"
    ],
    index: [
      "Vai trò hiện tại",
      'Cấp bậc hiện tại của bạn là  <i style="color:#1989fa">{currVip}</i><br>Hiện tại chỉ có thể nhận <i style="color:#1989fa">{currVip}</i>nhiệm vụ cấp <br>是否加入 <i style="color:#dd6161">{vip}</i> cấp',
      "Gia nhập ngay",
      "Vui lòng chọn loại nhiệm vụ"
    ],
    list: ["Bảng nhiệm vụ", "Bên có nhu cầu", "Còn dư", "Yêu cầu", "Nhận "],
    show: [
      "Chi tiết nhiệm vụ",
      "Người đã kiếm được",
      "Còn dư {num} suất",
      "Xét duyệt trong vòng 48 tiếng",
      "Mô tả nhiệm vụ",
      "Bên có nhu cầu",
      "Tiêu chuẩn xét duyệt",
      "Sao chép ",
      "Chuyển qua",
      "Các bước nhiệm vụ",
      "Bước thứ{index}",
      "Mẫu xét duyệt",
      "Không có mẫu xét duyệt",
      "Đang tải dữ liệu...",
      "Đang gửi đi...",
      "Nhận nhiệm vụ",
      "Đăng nhập ngay",
      "Đăng nội dung",
      "Chi tiết sản phẩm",
      "Hình ảnh sản phẩm",
      "Tiêu đề sản phẩm",
      "Hoàn tiền",
      "Giá",
      "Đã bán",
      "Mua ngay",
      "Xác nhận mua hàng",
      "Xác nhận mua",
      "Hủy",
      "Bạn có chắc chắn muốn mua nhiệm vụ này không?",
      "Sau khi mua sẽ trừ số tiền tương ứng",
      "Mua thành công",
      "Về trang chủ",
      "Mua nhiệm vụ thành công!",
      "Bạn có thể xem tiến độ trong Nhiệm vụ của tôi",
      "Giá mua:",
      "Hoa hồng nhận được:",
      "Yêu cầu cấp độ",
      "Cấp độ không đủ",
      "Đang tải thông tin nhiệm vụ",
      "Nhiệm vụ đã được nhận",
      "Nhiệm vụ đã đầy",
      "Tạm thời không thể mua",
      "Nhiệm vụ này đã được nhận, vui lòng chọn nhiệm vụ khác",
      "Nhiệm vụ này đã đầy, vui lòng kiểm tra các nhiệm vụ khác",
      "Vui lòng thử lại sau hoặc liên hệ dịch vụ khách hàng",
      "Nhấp để xem chi tiết",
      "Cấp độ không khớp, không thể mua",
      "Nhiệm vụ này yêu cầu cấp độ {requiredLevel}, cấp độ hiện tại của bạn không khớp",
      "Nhiệm vụ này yêu cầu cấp độ {requiredLevel}, hiện tại bạn là {currentLevel}, cấp độ không khớp không thể mua",
      "Thành viên đã hết hạn",
      "Thành viên của bạn đã hết hạn, vui lòng gia hạn trước khi mua nhiệm vụ",
      "Thành viên của bạn đã hết hạn, vui lòng gia hạn trước khi mua nhiệm vụ",
      "Gia hạn ngay"
    ],
  },
  serviceCenter: [
    "Trung tâm chăm sóc khách hàng",
    "Hi，Tôi là nhân viên chăm sóc khách hàng",
    "Rất vui khi được phục vụ quý khách",
    "Dịch vụ tự động",
    "Chăm sóc khách hàng trực tuyến",
    "Chăm sóc khách hàng nạp tiền",
    "Chăm sóc khách hàng Line"
  ],
  audit: {
    default: [
      "Xét duyệt nhiệm vụ",
      "Người dùng nhận",
      "Ngày nhận",
      "Ngày cập nhật",
      "Xét duyệt"
    ],
    tabs: ["Đang tiến hành", "Đang xét duyệt", "Đã hoàn thành", "Đã thất bại"],
    info: [
      "Chi tiết xét duyệt",
      "Tiêu đề nhiệm vụ",
      "Số tiền nhiệm vụ",
      "Người đã hoàn thành",
      "Còn dư {num}suất",
      "Mô tả nhiệm vụ",
      "Thông tin liên kết",
      "Mẫu xét duyệt",
      "Người dùng nhận",
      "Nhận",
      "Trạng thái hoàn thành",
      "Mẫu gửi đi",
      "Mẫu người dùng chưa gửi đi",
      "Ngày cập nhật",
      "Đang tải dữ liệu...",
      "Cố ý",
      "Duyệt lại",
      "Thất bại",
      "Thành công",
      "Xét duyệt nhiệm vụ",
      "Mô tả xét duyệt",
      "Vui lòng nhập mô tả xét duyệt",
      "Nhiệm vụ gửi đi không hợp lệ，Vui lòng gửi lại để duyệt",
      "Chúc mừng bạn đã hoàn thành nhiệm vụ，tiếp tục thôi nào",
      "Ảnh chụp màn hình giao diện nhiệm vụ gửi đi không đúng,nhiệm vụ không thành công",
      "Nhiệm vụ gửi nhiều lần，nhiệm vụ không thành công"
    ],
  },
  postRecord: [
    "Quản lý đăng",
    "Đăng",
    "Tổng số lượng",
    "Đã hoàn thành ",
    "Ngày kết thúc",
    "Xét duyệt",
    "Thu hồi",
    "Sửa"
  ],
  wallet: {
    default: [
      "Ví của tôi",
      "Nạp tiền",
      "Rút tiền",
      "Lịch sử nạp tiền",
      "Lịch sử rút tiền",
      "Nạp",
      "Rút",
      "Alipay"
    ],
    label: [
      "Phương thức rút tiền",
      "Số tiền rút ra",
      "Mật khẩu thanh toán",
      "Gửi đi",
      "Số điện thoại",
      "Email",
      "IFSC",
      "Ngân hàng rút tiền",
      "Phí rút tiền"
    ],
    placeholder: [
      "Chọn phương thức rút tiền",
      "Vui lòng nhập số tiền rút ra",
      "Vui lòng nhập mật khẩu thanh toán",
      "Vui lòng chọn phương thức rút tiền",
      "Vui lòng nhập số điện thoại người nhận tiền",
      "Vui lòng nhập email người nhận tiền",
      "Vui lòng nhập IFSC người nhận tiền",
      "Vui lòng chọn ngân hàng rút tiền"
    ],
    msg: [
      "Bạn vẫn chưa cài đặt mật khẩu thanh toán ，vui lòng cài đặt trước",
      "Bạn vẫn chưa liên kết thẻ ngân hàng，vui lòng liên kết trước"
    ],
    info: {
      currentBalance: "Số Dư Hiện Tại",
      withdrawAmount: "Số Tiền Rút",
      fee: "Phí",
      totalDeduction: "Tổng Khấu Trừ"
    },
    warning: {
      insufficientFunds: "Số Dư Không Đủ",
      shortfall: "Thiếu Hụt",
      insufficientBalance: "Số dư không đủ! Số tiền rút {currency}{withdrawAmount}, số dư hiện tại {currency}{currentBalance}, thiếu hụt {currency}{shortfall}",
      insufficientBalanceWithFee: "Số dư không đủ! Số tiền rút {currency}{withdrawAmount} + phí {currency}{fee} = tổng cộng {currency}{totalRequired}, số dư hiện tại {currency}{currentBalance}, thiếu hụt {currency}{shortfall}"
    },
  },
  recharge: {
    default: [
      "Nạp tiền",
      "Chi tiết nạp tiền",
      "Lịch sử nạp tiền",
      "Số dư khả dụng{money}，vui lòng chọn phương thức nạp tiền",
      "Sô tiền tối thiểu mỗi giao dịch{currency}{min}，tối đa {currency}{max}，phí thủ tục {fee}%",
      "Đang gửi đi...",
      "Nạp ngay",
      "Quay lại",
      "Đang tải..."
    ],
    info: [
      "Số tiền nạp",
      "Số đơn hàng",
      "Ngân hàng nhận",
      "Tài khoản nhận",
      "Người nhận",
      "Tên người thanh toán",
      "Số điện thoại người thanh toán",
      "Tài khoản UPI người thanh toán",
      "Email người thanh toán",
      "Tên ngân hàng",
      "Loại tài khoản",
      "Ghi chú"
    ],
    placeholder: [
      "Vui lòng nhập số tiền nạp",
      "Vui lòng chọn kênh nạp tiền",
      "Vui lòng nhập tên chuyển khoản",
      "Số tiền tối thiểu mỗi giao dịch là {currency}{min}",
      "Số tiền tối đa mỗi giao dịch là {currency}{max}",
      "Vui lòng nhập tên người thanh toán",
      "Vui lòng nhập số điện thoại người thanh toán, thêm mã quốc tế, ví dụ: 86",
      "Vui lòng nhập tài khoản UPI người thanh toán",
      "Vui lòng nhập email người thanh toán"
    ],
    label: [
      "Số tiền nạp",
      "Kênh nạp tiền",
      "Tên chuyển khoản",
      "Tên người thanh toán",
      "Số điện thoại người thanh toán",
      "Tài khoản UPI người thanh toán",
      "Email người thanh toán"
    ],
    tips: [
      "Vui lòng chọn phương thức sau để chuyển số tiền tương ứng để tránh chậm trễ trong việc thu thập tài chính<br>Sau khi chuyển khoản, vui lòng tải lên ảnh chụp màn hình chuyển khoản làm bằng chứng xác minh",
      "Không cần thêm bạn bè, quét mã QR để chuyển tiền cho tôi",
      "Vui lòng hoàn thành việc chuyển khoản theo thông tin dưới đây",
      "Mẹo: Sau khi thanh toán thành công, vui lòng liên hệ dịch vụ khách hàng trực tuyến và cung cấp tài khoản thành viên, số tiền nạp, số đơn hàng, tài khoản người gửi, thời gian nạp tiền; để tạo điều kiện cho việc bổ sung tiền kịp thời bởi tài chính",
      "Lưu ý: Thành viên vui lòng gửi mỗi khoản thanh toán chuyển khoản một lần",
      "Sau khi chuyển khoản, vui lòng tải lên ảnh chụp màn hình chuyển khoản làm bằng chứng xác minh",
      "Vui lòng chọn phương thức sau để chuyển số tiền tương ứng",
      "Vui lòng tải lên ảnh chụp màn hình chuyển khoản"
    ]
  },
  dialog: [
    "Lời nhắc nhở",
    "Xác định",
    "Đang gửi đi...",
    "Sao chép thành công",
    "Phiên bản hệ thống IOSquá cũ không hỗ trợ",
    "Đang đăng kí...",
    "Đang tải dữ liệu...",
    "Giá trị đầy đủ"
  ],
  lineList: ["Chọn tuyến", "Tuyến hiện tại", "Tuyến"],
  newLc: [
    "Truyền thuyết",
    "Tài khoản",
    "Khoảng thời gian",
    "ngày",
    "thu nhập mỗi ngày",
    "Gửi ngân phiếu",
    "Xin hãy nhập số tiền đặt cọc",
    "lợi nhuận",
    "Gởi",
    "Tài chính",
    "Xin hãy điền thông tin",
    "Hãy chọn sản phẩm trước",
    "Hồ sơ mua"
  ],
  newLcList: [
    "Hồ sơ mua",
    "Không ghi âm",
    "lợi nhuận",
    "bao nhiêu tiền",
    "Giờ mua",
    "Bang"
  ],
  wheel: [
    "Xin chúc mừng, bạn đã trúng số {name},Hệ thống đã phân phối phần thưởng",
    "Xin chúc mừng, bạn đã trúng số {name},Vui lòng liên hệ bộ phận chăm sóc khách hàng để nhận giải thưởng",
    "Hãy tiếp tục làm việc tốt và bạn sẽ giành được chiến thắng trong lần tiếp theo",
    "Ghi chú rút thăm may mắn",
    "Giành được",
    "Bàn xoay lớn",
    "Số lần quay còn lại: ",
    "Bắt đầu",
    "Số lần quay còn lại không đủ"
  ],
  appMsg: [
    "Chuẩn bị tải xuống cập nhật...",
    "Tải xuống hoàn tất. Có cài đặt gói cập nhật không?",
    "Tải xuống cập nhật thất bại",
    "Đã tải xuống {num}%",
    "Đang cập nhật...",
    "Cập nhật thành công. Sẽ khởi động lại sớm",
    "Cập nhật thất bại",
    "Nhấn lại để thoát ứng dụng"
  ],
  appDown: [
    "Tải xuống ứng dụng",
    "Quét mã QR để tải xuống ứng dụng",
    "Lưu mã QR",
    "Lưu mã QR thành công",
    "Lưu mã QR thất bại, vui lòng thử nhiều lần hoặc lưu ảnh chụp màn hình"
  ],
  buyVip: [
    "Đại lý mua VIP",
    "Tài khoản nạp tiền",
    "Vui lòng nhập số tài khoản nạp tiền",
    "Nạp tiền VIP",
    "Vui lòng chọn nạp tiền VIP",
    "Bạn có chắc chắn nạp tiền {grade} cho thành viên {user}?",
    "Gửi"
  ],
  Activity: [
    "Like Share Lễ hội Triệu Đô",
    "Đang tải trang..."
  ],
  usdt: ["Địa chỉ ngân hàng", "địa chỉ ngân hàng bị khóa", "Thêm địa chỉ ngân hàng"],
  wheelRecords: {
    title: "Hồ Sơ Trúng Thưởng",
    unknownPrize: "Giải Thưởng Không Xác Định",
    times: " lần",
    prizeTypes: {
      task: "Phần Thưởng Nhiệm Vụ",
      money: "Phần Thưởng Xu",
      other: "Phần Thưởng Khác"
    },
    status: {
      received: "Đã Nhận",
      pending: "Đang Chờ",
      unknown: "Không Xác Định"
    }
  },

  // Thanh toán Toàn cầu
  globalPay: {
    title: "Thanh toán Toàn cầu",
    description: "Chọn phương thức thanh toán của bạn để nạp tiền an toàn và tiện lợi",
    selectCountry: "Chọn Quốc gia",
    selectCountryPlaceholder: "Vui lòng chọn quốc gia",
    selectCountrySubtitle: "Chọn quốc gia hoặc khu vực của bạn",
    selectPaymentMethod: "Chọn Phương thức Thanh toán",
    selectPaymentSubtitle: "Chọn phương thức thanh toán bạn ưa thích",
    paymentAmount: "Số tiền Thanh toán",
    enterAmountSubtitle: "Nhập số tiền bạn muốn nạp",
    orderNumber: "Số Đơn hàng",
    paymentStatus: "Trạng thái Thanh toán",

    steps: {
      selectCountry: "Chọn Quốc gia",
      selectPayment: "Chọn Thanh toán",
      enterAmount: "Nhập Số tiền"
    },

    quickAmounts: "Số tiền Nhanh",
    amountRange: "Phạm vi Số tiền",
    orderSummary: "Tóm tắt Đơn hàng",
    country: "Quốc gia",
    paymentMethod: "Phương thức Thanh toán",
    amount: "Số tiền",
    fee: "Phí",
    feeRate: "Tỷ lệ Phí",
    total: "Tổng cộng",

    tabs: {
      select: "Chọn Thanh toán",
      processing: "Đang xử lý",
      completed: "Hoàn thành"
    },

    countries: {
      ID: "Indonesia",
      IN: "Ấn Độ",
      TH: "Thái Lan",
      VN: "Việt Nam",
      MY: "Malaysia",
      BR: "Brazil",
      CN: "Trung Quốc",
      TW: "Đài Loan",
      HK: "Hồng Kông",
      PH: "Philippines",
      SG: "Singapore"
    },

    paymentMethods: {
      wallet: "Ví Điện tử",
      bank: "Chuyển khoản Ngân hàng",
      card: "Thẻ Ngân hàng",
      scan: "Thanh toán QR",
      online: "Ngân hàng Trực tuyến",
      upi: "Thanh toán UPI",
      qris: "Thanh toán QRIS",
      ovo: "Ví OVO",
      dana: "Ví DANA",
      gopay: "Ví GoPay",
      shopeepay: "ShopeePay",
      linkaja: "LinkAja",
      paytm: "Paytm",
      phonepe: "PhonePe",
      googlepay: "Google Pay",
      truemoney: "TrueMoney",
      promptpay: "PromptPay",
      momo: "MoMo",
      zalopay: "ZaloPay",
      grabpay: "GrabPay",
      boost: "Boost",
      tng: "Touch 'n Go",
      pix: "PIX",
      boleto: "Boleto"
    },

    status: {
      pending: "Đang chờ",
      processing: "Đang xử lý",
      completed: "Hoàn thành",
      failed: "Thanh toán Thất bại",
      cancelled: "Đã hủy",
      expired: "Đã hết hạn"
    },

    messages: {
      loading: "Đang tải...",
      selectCountryFirst: "Vui lòng chọn quốc gia trước",
      selectPaymentMethodFirst: "Vui lòng chọn phương thức thanh toán trước",
      enterAmount: "Vui lòng nhập số tiền thanh toán",
      noPaymentMethods: "Không có phương thức thanh toán nào khả dụng cho quốc gia này",
      processing: "Đang xử lý...",
      amountTooLow: "Số tiền thanh toán không được thấp hơn {min}",
      amountTooHigh: "Số tiền thanh toán không được vượt quá {max}",
      orderCreated: "Tạo đơn hàng thành công",
      paymentSuccess: "Thanh toán thành công",
      paymentFailed: "Thanh toán thất bại",
      networkError: "Lỗi mạng, vui lòng thử lại",
      redirecting: "Đang chuyển hướng đến trang thanh toán..."
    },

    buttons: {
      payNow: "Thanh toán Ngay",
      cancel: "Hủy",
      retry: "Thử lại",
      back: "Quay lại",
      confirm: "Xác nhận",
      selectCountry: "Chọn Quốc gia",
      selectPayment: "Chọn Phương thức Thanh toán"
    },

    selectBank: "Chọn ngân hàng",

    placeholders: {
      searchCountry: "Tìm kiếm Quốc gia",
      enterAmount: "Nhập Số tiền"
    },

    tips: {
      selectCountryTip: "Vui lòng chọn quốc gia của bạn để hiển thị các phương thức thanh toán có sẵn",
      paymentMethodTip: "Chọn phương thức thanh toán phù hợp nhất với bạn",
      amountTip: "Tối thiểu {min}, Tối đa {max} mỗi giao dịch",
      processingTip: "Đang xử lý thanh toán, vui lòng không đóng trang",
      successTip: "Thanh toán thành công, tiền sẽ đến trong 5-10 phút"
    }
  }
};
