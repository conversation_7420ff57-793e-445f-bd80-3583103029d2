export default {
  line: "Tukar talian",
  language: "Pilih Bahasa",
  signin: "Daftar masuk",
  baozhengjin: "保证金",
  unbaozhengjin: "解冻保证金",
  common: [
    "perkhidmatan dalam talian",
    "Tidak ditangguhkan",
    "Lukisan bertuah",
    "Pemberitahuan",
    "Te<PERSON>pan",
    "Bantuan",
    "Mengenai",
    "Sahkan"
  ],
  home: {
    broadcast:
      "Tahniah kepada ahli {member}<br> kerana mengesyorkan {vipname}<br> untuk mendapatkan hadiah promosi {currency} {grade}!",
    taskHall: {
      title: ["<PERSON><PERSON>", "<PERSON><PERSON>"],
    },
    memberList: {
      title: "<PERSON><PERSON><PERSON> ahli",
      data: [
        "Tahniah: {member}",
        "Selesaikan {num} pesanan hari ini dan dapatkan {currency} {profit}!"
      ],
    },
    businessList: {
      title: "Senarai peniaga",
      data: ["{member}", "{Num} tugas tunggal dikeluarkan hari ini"],
    },
    noticeTitle: "Pet<PERSON>",
    tabBar: ["<PERSON>uma<PERSON>", "Tugas", "VIP", "Pendapatan", "Saya"],
    menu: ["Kawasan VIP", "Tutorial Video", "Ganjaran Promosi"],
    msg: "Tugas tidak dibuka",
    video: "Belum ada tutorial video",
    // Terjemahan baru halaman utama
    notification: "Tingkatkan jualan produk untuk mencapai keuntungan",
    noMoreData: "Tiada lagi data",
    noTaskData: "Tiada data tugas",
    defaultTaskTitle: "Tajuk Tugas",
    commissionTag: "Komisen",
    logoText: "Cobwe",
    teamTab: "Pasukan",
    // Langkah proses
    processSteps: [
      "Sertai Ahli",
      "Pilih Produk",
      "Tempah",
      "Dapatkan Komisen"
    ],
    // Tab kategori
    allCategory: "Semua",
    // Butang isih
    priceSort: "Harga"
  },
  login: {
    text: [
      "Ingat nama pengguna / kata laluan",
      "log masuk ...",
      "log masuk dengan segera",
      "log masuk"
    ],
    placeholder: [
      "Sila masukkan nombor telefon atau e-mel",
      "Sila masukkan kata laluan anda"
    ],
    i18n: ["Tidak ada {title} akaun? {A} {line}", "berdaftar"],
  },
  register: {
    text: [
      "Selamat datang untuk mendaftar",
      "Kod SMS menghantar ...",
      "Dapatkan Kod SMS",
      "Mendaftar ...",
      "Daftarlah sekarang",
      "Sudah mempunyai akaun, muat turun sekarang",
      "Setuju untuk membuang klausul",
      "Pendaftaran bimbit",
      "pendaftaran e-mel",
      "Dapatkan kod e-mel",
      "Hantar kod e-mel..."
    ],
    placeholder: [
      "Sila masukkan nombor telefon",
      "Sila masukkan kod pengesahan SMS",
      "Sila masukkan kata laluan anda",
      "Sila sahkan kata laluan anda",
      "Sila masukkan kod jemputan",
      "Kedua-dua kata laluan itu berbeza",
      "sila masukkan kod pengesahan",
      "Sila masukkan nombor peti mel",
      "Sila semak klawsul pembatalan setuju",
      "Sila masukkan kod pengesahan emel"
    ],
    i18n: ["Sudah mempunyai akaun? {A} {line}", "log masuk"],
  },
  postTask: {
    navBar: {
      title: "Pasca Tugas",
      right: "Peraturan Penerbitan",
    },
    field: [
      {
        label: "Pengelasan Tugas",
      },
      {
        label: "Tajuk Tugas",
        placeholder: "Sila masukkan tajuk tugas",
        error: "Tajuk tugas tidak boleh kosong",
      },
      {
        label: "Pengenalan Tugas",
        placeholder: "Sila masukkan profil tugas",
      },
      {
        label: "Harga Unit Tugas",
        placeholder: "Sila masukkan harga unit tugas",
        right: "{currency}",
        error: [
          "Harga unit tugas tidak boleh kosong",
          "Harga unit tugas tidak boleh lebih rendah daripada 1 {currency}"
        ],
      },
      {
        label: "Kuantiti yang diterima",
        placeholder: "Sila masukkan jumlah yang diterima",
        error: "Kuantiti yang diterima tidak boleh kosong",
      },
      {
        label: "Bilangan Tuntutan",
        placeholder: "Sila masukkan berapa kali koleksi",
        right: "Kali / orang",
        error: "Waktu penerimaan tidak boleh kosong",
      },
      {
        label: "Harga Jumlah Tugas",
        error: "Jumlah harga tugas tidak dikira",
      },
      {
        label: "Maklumat Pautan",
        placeholder: "Sila masukkan alamat pautan",
        error: "Maklumat pautan tidak boleh kosong",
      },
      {
        label: "Tahap Tugas",
      },
      {
        label: "tarikh akhir",
        placeholder: "Klik untuk memilih tarikh",
        error: "Tarikh akhir tidak boleh kosong",
      },
      {
        label: "Syarat Penyelesaian",
        error: "Sila pilih syarat penyelesaian",
      },
      {
        label: "Keperluan Muat Naik",
        placeholder: "Sila masukkan keperluan muat naik",
      },
      {
        label: "Contoh Audit",
      },
      {
        label: "Langkah Operasi",
        placeholder: "Langkah mudah dan senang difahami dapat diselesaikan!",
        error: "Langkah operasi tidak boleh kosong",
        img: "Gambar contoh"
      },
    ],
    button: "Hantar",
    step: {
      title: "Langkah Operasi",
      left: "Batal",
      right: "Selesai",
      placeholder: "Sila masukkan keterangan langkah",
      button: ["Padam", "Tambah"],
    },
    tips: [
      "Bayaran platform adalah sekitar {pump} {br} Pelepasan ini memerlukan pembayaran sekitar {harga}, pastikan baki akaun anda mencukupi {a} {br} Peringatan: Platform ini melarang penerbitan pornografi, perjudian, dadah, politik, dan semua undang-undang lain yang dilarang Isi kandungannya, jika didapati mata ditolak, akaun akan dilarang.",
      "Isi semula",
      "Simpan sekurang-kurangnya satu langkah",
      "Tambahkan hingga sepuluh langkah"
    ],
  },
  vip: {
    user: {
      title: ["Identiti Anda", "Pelancong"],
      label: "Tugasan harian",
      value: [
        "Tarikh berkuatkuasa",
        "Berkuat kuasa kekal",
        "Sila log masuk dahulu"
      ],
    },
    list: {
      label: "Tugas harian: {number} kali",
      button: "Sertai sekarang",
      text: [
        "Keuntungan Harian",
        "Pendapatan bulanan",
        "Ganjaran Syor: Semua orang",
        "percuma",
        "Setiap pesanan"
      ],
    },
    dialog: [
      "Adakah anda pasti membelanjakan {currency} {number} untuk menjadi {name}? ",
      " Anda sekarang {currname} dan tidak boleh menjadi {name} ",
      " Adakah anda pasti membelanjakan {currency} {jumlah} untuk memperbaharui {name}? "
    ],
    // Butang teks
    buttons: {
      payNow: "Bayar Sekarang",
      renewNow: "Perbaharui Sekarang",
      buyNow: "Beli Sekarang"
    },
    // Halaman VIP terjemahan baru
    expireTime: "Masa tamat tempoh",
    benefitsTitle: "Keistimewaan Eksklusif",
    loading: "Memuatkan...",
    loadingDesc: "Mendapatkan maklumat tahap keahlian...",
    exclusivePrivilege: "Keistimewaan Eksklusif {name}",
    upgradeCost: "Kos naik taraf",
    memberLevel: "Tahap keahlian",
    levelAuth: "Pengesahan keahlian tahap {level}",
    upgradeNow: "Naik taraf sekarang untuk menikmati layanan VIP eksklusif!",
    referralReward: "Ganjaran komisen rujukan",
    referralDesc: "Untuk setiap pengguna baru yang berjaya dirujuk, anda akan menerima",
    referralPercent: "Ganjaran komisen rujukan {percent}%",
    inviteMore: "Semakin banyak jemputan, semakin kaya ganjaran!",
    // Butang teks
    buttons: {
      payNow: "Bayar Sekarang",
      renewNow: "Perbaharui Sekarang",
      buyNow: "Beli Sekarang"
    },
    // Mesej status
    vipLocked: "Tahap VIP ini dikunci dan tidak boleh dibeli",
    expired: "Tamat Tempoh",
    expireTime: "Masa Tamat Tempoh",
    // Butiran faedah
    benefits: {
      teamSize: "Saiz Pasukan Eksklusif",
      teamSizeDesc: "Uruskan sehingga <strong>{number} orang</strong> dalam pasukan eksklusif anda",
      teamManagement: "Nikmati pendapatan tambahan daripada pengurusan pasukan",
      teamIncome: "Menjadi ketua pasukan, gandakan pendapatan anda!",
      dailyTasks: "Had Tugasan Harian",
      dailyTasksDesc: "Boleh menyelesaikan sehingga <strong>{number} tugasan</strong> sehari",
      taskRewards: "Nikmati lebih banyak peluang pendapatan tugasan",
      earnMore: "Lebih banyak tugasan, lebih banyak pendapatan!",
      lotteryTimes: "Peluang Loteri Harian",
      lotteryTimesDesc: "Dapatkan <strong>{times} peluang loteri</strong> sehari",
      lotteryChance: "Lebih banyak peluang, lebih banyak kemungkinan menang",
      lotteryRewards: "Roda bertuah, kejutan tanpa had!"
    },
  },
  user: {
    default: [
      "Atasan saya",
      "Akaun log masuk",
      "Kod jemputan",
      "Log keluar",
      "Baki",
      "Syiling emas",
      "Dompet saya",
      "Disekat",
      "Terhad",
      "Baik",
      "Cemerlang"
    ],
    myEarnings: {
      grid: [
        "Baki Peribadi",
        "Pendapatan Semalam",
        "Pendapatan Hari Ini",
        "Pendapatan Minggu Ini",
        "Pendapatan Bulan Ini",
        "Pendapatan Bulan Lalu",
        "Pendapatan Jumlah",
        "Tugas Selesai Hari Ini (Bujang)",
        "Hari ini Tinggal tugas (tunggal) ",
        "Komisen hari ini",
        "Rebat hari ini"
      ],
    },
    default: [
      "Atasan Saya",
      "Masuk Akaun",
      "Kod Jemputan",
      "log keluar",
      "Seimbang",
      "emas",
      "Dompet saya",
      "tajuk",
      "had",
      "baik",
      "sangat baik"
    ],
    menu: [
      "Rekod Tugas",
      "Tugas Audit",
      "Pengurusan Pelepasan",
      "Maklumat peribadi",
      "Mengikat Akaun",
      "Penyata Harian",
      "Rekod Perubahan Akaun",
      "menjemput kawan-kawan",
      "Laporan Pasukan",
      "Manual Bantuan",
      "Pusat Kredit",
      "Muat turun APP",
      "Bayaran VIP pihak ketiga"
    ],
    YUEBAO: [
      "Yuebao",
      "Pemindahan dalam kaedah",
      "Masa storan",
      "hari",
      "Daftar setiap hari",
      "Jumlah deposit",
      "keuntungan",
      "Sila masukkan jumlah deposit",
      "tentukan",
      "Sila pilih maklumat",
      "Sila pilih produk dahulu",
      "bagus",
      "robot",
      "Yuebao"
    ],
    newlist: [
      "Tiada rekod",
      "keuntungan",
      "jumlah wang",
      "Masa pembelian",
      "keadaan"
    ],
    robot: [
      "Penjaga rumah awan",
      "Keterangan Servis",
      "1. Gaji perkhidmatan pembukaan adalah 99 / bulan, berkesan pada hari berikutnya",
      "2. Selepas ia berkesan, ia akan automatik selesaikan tugas harian untuk pelanggan, dan automatik selesaikan tugas harian dan menetapkan pendapatan sebelum 8 pagi setiap hari",
      "Pembantu rumah Awan Buka"
    ],
    // Terjemahan khusus halaman saya
    myPage: {
      account: "Akaun",
      inviteCode: "Kod Jemputan",
      logout: "Log keluar",
      balance: "Baki",
      myWallet: "Dompet Saya",
      totalEarnings: "Jumlah Pendapatan",
      todayRemaining: "Pendapatan Hari Ini",
      luckyDraw: "Lukisan Bertuah",
      inviteFriends: "Jemput Kawan"
    },
    // Banner jemputan
    inviteBanner: {
      title: "Jemput kawan dan dapatkan ganjaran",
      subtitle: "Kongsi kod jemputan anda dan dapatkan komisen"
    }
  },
  userInfo: {
    default: [
      "Maklumat peribadi",
      "Potret",
      "nombor telefon",
      "Kad bank",
      "Alipay",
      "perincian",
      "kata laluan masuk",
      "Kata Laluan Dana",
      "Klik untuk menetapkan",
      "Ubah suai avatar",
      "Ubah Kata Laluan Log Masuk",
      "Ubah Kata Laluan Dana",
      "serah",
      "Kosongkan cache"
    ],
    label: [
      "Kata Laluan Log Masuk Asal",
      "Kata laluan masuk baru",
      "sahkan kata laluan",
      "Kata Laluan Dana Asal",
      "Kata Laluan Dana Baru",
      "sahkan kata laluan"
    ],
    placeholder: [
      "Sila masukkan kata laluan masuk asal",
      "Sila masukkan kata laluan masuk baru",
      "Sila sahkan kata laluan masuk",
      "Sila masukkan kata laluan dana asal",
      "Sila masukkan kata laluan dana baru",
      "Sila sahkan kata laluan dana"
    ],
  },
  bankCard: {
    default: [
      "Kad Bank Mengikat",
      "Sahkan Sekarang",
      "sedang menghantar ...",
      "Tambahkan sekarang",
      "Tambah Kad Bank"
    ],
    tips: [
      "Sila tambah kad bank selepas pengesahan nama sebenar",
      "Nama pembukaan akaun kad bank terikat anda harus sama dengan nama sebenar anda yang telah disahkan, jika tidak, pengeluaran akan gagal."
    ],
    label: [
      "Nama",
      "Nama bank",
      "Akaun bank",
      "Jenis bank",
      "Kod IFSC",
      "nombor telefon",
      "kotak mel",
      "Alamat dompet"
    ],
    placeholder: [
      "Sila pilih nama bank",
      "Sila masukkan akaun bank anda",
      "Sila pilih jenis bank",
      "Sila masukkan kod IFSC",
      "Sila masukkan nombor telefon bimbit",
      "Sila masukkan e-mel",
      "Sila masukkan alamat dompet"
    ],
  },
  userSet: {
    default: ["Disahkan", "perincian", "Mengikat Alipay", "serah"],
    label: ["nama sebenar", "Nombor QQ", "Akaun Alipay", "Nama Alipay"],
    placeholder: [
      "Masukkan nama sebenar anda (untuk penarikan nama anda)",
      "Sila masukkan nombor QQ",
      "Sila masukkan akaun Alipay anda",
      "Sila masukkan nama Alipay anda"
    ],
    tips:
      "Peringatan: Kad bank terikat tidak dapat diubah, ia digunakan untuk pengeluaran anda",
  },
  bindAccount: {
    default: ["Akaun yang mengikat", "Lihat Tutorial", "serah"],
    tabs: ["nombor akaun", "nombor akaun", "nombor akaun"],
    label: ["Arahan", "Tambah tangkapan skrin", "nombor akaun"],
    placeholder: "Sila masukkan {account} nombor akaun",
  },
  dayReport: [
    "Penyata Harian",
    "Jumlah hasil",
    "Tugas saya selesai",
    "Pendapatan misi saya",
    "Tugas diselesaikan oleh pekerja bawahan",
    "Pendapatan tugas rendah",
    "tunggal",
    "30 hari terakhir",
    "Kuantiti",
    "tugas",
    "Bawahan",
    "penggunaan",
    "Tarikh"
  ],
  fundRecord: {
    default: [
      "Rekod Perbelanjaan",
      "Isi semula rekod",
      "Rekod Pendapatan",
      "Caj",
      "Terima",
      "sokongan"
    ],
    tabs: ["pendapatan", "perbelanjaan", "Isi semula"],
    tradeDescription: "Penerangan Perdagangan"
  },
  vanPull: ["Tidak ada lagi data", "tiada data"],
  promote: [
    "Kawan awak",
    "Jemput anda untuk menyertai {title}",
    "Kod Pengesyoran",
    "Salin Kod Rujukan",
    "Salin pautan jemputan",
    "Simpan Kod QR",
    "Ganjaran Promosi",
    "Simpan poster dengan jayanya",
    "Gagal menyimpan poster, sila cuba beberapa kali lagi atau simpan tangkapan skrin",
    "Sila ambil tangkapan skrin",
    "Simpan Poster Promosi",
    "Simpan poster promosi ke telefon Anda <br> Jika Anda tidak berjaya, cuba beberapa kali lagi atau simpan tangkapan skrin"
  ],
  // Pengantarabangsaan halaman jemputan
  invite: {
    inviteFriends: "Jemput Kawan",
    yourInviteCode: "Kod Jemputan Anda",
    copyInviteCode: "Salin Kod Jemputan",
    inviteLink: "Pautan Jemputan",
    copyLink: "Salin Pautan",
    share: "Kongsi",
    shareInviteNow: "Kongsi Jemputan Sekarang",
    qrCode: "Kod QR",
    saveSuccess: "Berjaya Disimpan",
    saveFailed: "Gagal Disimpan, Sila Cuba Lagi",
    copySuccess: "Berjaya Disalin",
    copyFailed: "Gagal Disalin",
    shareTitle: "Jemput Kawan untuk Mendapat Syiling",
    shareDescription: "Sertai kami dan dapatkan ganjaran hebat bersama!",
    inviteRewards: "Ganjaran Jemputan",
    howToInvite: "Cara Menjemput",
    inviteSteps: {
      step1: "Kongsi kod atau pautan jemputan dengan kawan",
      step2: "Kawan mendaftar dan menyelesaikan tugas pertama",
      step3: "Anda mendapat ganjaran rujukan yang murah hati"
    }
  },
  teamReport: {
    default: [
      "carian untuk",
      "Pendapatan Rujukan",
      "Komisi Tugas",
      "Recharge Pasukan",
      "Pengunduran pasukan",
      "Bilangan Bayaran Pertama",
      "Bilangan Debut",
      "Bilangan Pasukan",
      "Pasukan Baru",
      "A",
      "Saya",
      "pengguna",
      "Isi semula",
      "tarik balik",
      "Rebat",
      "Komisen",
      "Pilih tahun, bulan dan hari",
      "Tiada data",
      "Jumlah caj semula",
      "Nombor Isi Ulang",
      "Rebate Isi Semula"
    ],
    tabs: ["Laporan Pasukan", "pasukan saya"],
    team: ["Bawahan Tahap 1", "Bawahan Tahap 2", "Bawahan Tahap 3"],
    structure: "Struktur Pasukan",
    details: "Butiran Bawahan",
    loading: "Memuatkan data pasukan",
    noData: "Tiada data pasukan",
    userDefault: "Pengguna",
    groupId: "ID Kumpulan",
    commission: "Komisen",
    unknown: "Tidak Diketahui",
    // Butiran ahli popup
    memberDetail: "Butiran Ahli",
    basicInfo: "Maklumat Asas",
    earningsInfo: "Maklumat Pendapatan",
    teamInfo: "Maklumat Pasukan",
    statusInfo: "Maklumat Status",
    username: "Nama Pengguna",
    userId: "ID Pengguna",
    phone: "Telefon",
    email: "E-mel",
    regTime: "Masa Pendaftaran",
    invitor: "Penjemput",
    totalCommission: "Jumlah Komisen",
    todayCommission: "Komisen Hari Ini",
    totalRecharge: "Jumlah Isi Semula",
    balance: "Baki",
    directCount: "Rujukan Langsung",
    teamCount: "Saiz Pasukan",
    status: "Status",
    vipLevel: "Tahap VIP",
    lastLogin: "Log Masuk Terakhir",
    statusActive: "Aktif",
    statusInactive: "Tidak Aktif",
    statusSuspended: "Digantung",
    statusBanned: "Dilarang",
    statusUnknown: "Status Tidak Diketahui"
  },
  help: ["Manual Bantuan", "belum ada kandungan"],
  credit: [
    "Pusat Kredit",
    "Penyata Kredit",
    "Sejarah Kredit",
    "<p> 1. Skor kredit dinilai sekali seminggu </p> <p> 2. Skor kredit pengguna awal: <b>60</p> <p> 3. Sekiranya pengguna dikesan melakukan tugas muat naik Gambar palsu dipotong sehari: <b> 1 </b> mata, had pemotongan maksimum: <b> 7 </b> mata </p> <p> 4. Sekiranya pengguna tidak dikesan menggunakan gambar palsu, tingkatkan <b> 1 </b> Points</p> <p> 5. Mata kredit lebih rendah daripada <b> 50 </b> mata akan ditarik balik </p> <p> 6. Mata kredit lebih rendah daripada <b> 30 </b> b> Jumlah tugas mengetuk menjadi separuh </p> <p> 7. Mata kredit kurang dari atau sama dengan <b> 0 </b> akan disekat </p> ",
    "Kredit Saya",
    "Tidak ada sejarah kredit buat sementara waktu"
  ],
  upload: [
    "memuat naik ...",
    "Format yang salah",
    "Berjaya dimuat naik",
    "muatnaik gagal"
  ],
  task: {
    default: [
      "Senarai Tugas",
      "Keperluan Tugas",
      "buat",
      "Audit",
      "Pautan Terbuka",
      "Salin pautan",
      "harga seunit",
      "serah",
      "mengalah"
    ],
    tabs: [
      "memproses",
      "ditinjau",
      "selesai",
      "Gagal",
      "Berniat jahat",
      "Ditinggalkan"
    ],
    msg: "Sila muat naik gambar menyelesaikan tugas",
    info: [
      "Butiran Tugas",
      "Tajuk Tugas",
      "Hasil Tugas",
      "perincian misi",
      "Permintaan muat naik",
      "Kirim Sampel",
      "Pengguna tidak menghantar sampel",
      "Arahan Ulasan",
      "Tarikh Semakan",
      "Sisi Permintaan",
      "lepaskan",
      "salinan",
      "Lompat",
      "Langkah Tugas",
      "Langkah {index}",
      "Contoh Audit",
      "Tiada sampel audit",
      "memuat ...",
      "Tinggalkan tugas",
      "Kirim untuk menyelesaikan tugas",
      "Isi Pos",
      "Kandungan yang diterbitkan"
    ],
    index: [
      "Identiti Semasa",
      'Tahap semasa anda adalah <i style = " color: #1989fa "> {currVip} </i> <br> Anda hanya dapat menerima <i style = " color: #1989fa "> {currVip} <sekarang / i> Tugas peringkat  Sama ada akan menyertai <i style = "color: #dd6161 "> {vip} </i> level ',
      "Sertai sekarang",
      "Sila pilih kategori tugas"
    ],
    list: ["senarai tugas", "Sisi Permintaan", "Sisa", "Tuntutan", "terima"],
    show: [
      "Butiran Tugas",
      "Orang telah memperoleh",
      "{Num} tempat yang tinggal",
      "Ulasan dalam masa 48 jam",
      "perincian misi",
      "Sisi Permintaan",
      "Piawaian Audit",
      "salinan",
      "Lompat",
      "Langkah Tugas",
      "Langkah {index}",
      "Contoh Audit",
      "Tiada sampel audit",
      "memuat ...",
      "sedang menghantar ...",
      "terima tugas",
      "log masuk segera",
      "Isi Pos",
      "Butiran Produk",
      "Imej Produk",
      "Tajuk Produk",
      "Pulangan Tunai",
      "Harga",
      "Terjual",
      "Beli Sekarang",
      "Pengesahan Pembelian",
      "Sahkan Pembelian",
      "Batal",
      "Adakah anda pasti mahu membeli tugas ini?",
      "Jumlah yang sesuai akan ditolak selepas pembelian",
      "Pembelian Berjaya",
      "Kembali ke Laman Utama",
      "Tugas berjaya dibeli!",
      "Anda boleh menyemak kemajuan dalam Tugas Saya",
      "Harga Beli:",
      "Komisen Diperoleh:",
      "Tahap Diperlukan",
      "Tahap Tidak Mencukupi",
      "Memuatkan Maklumat Tugas",
      "Tugas Sudah Diambil",
      "Tugas Penuh",
      "Tidak Tersedia Buat Masa Ini",
      "Tugas ini telah diambil, sila pilih tugas lain",
      "Tugas ini sudah penuh, sila periksa tugas lain",
      "Sila cuba lagi nanti atau hubungi perkhidmatan pelanggan",
      "Klik untuk butiran",
      "Tahap tidak sepadan, tidak boleh membeli",
      "Tugas ini memerlukan tahap {requiredLevel}, tahap semasa anda tidak sepadan",
      "Tugas ini memerlukan tahap {requiredLevel}, pada masa ini anda adalah {currentLevel}, tahap tidak sepadan tidak boleh membeli",
      "Keahlian Tamat Tempoh",
      "Keahlian anda telah tamat tempoh, sila perbaharui sebelum membeli tugas",
      "Keahlian anda telah tamat tempoh, sila perbaharui sebelum membeli tugas",
      "Perbaharui Sekarang"
    ],
  },
  serviceCenter: [
    "Khidmat Pelanggan",
    "Hai, perkhidmatan pelanggan eksklusif",
    "gembira dapat melayani anda",
    "layan diri",
    "perkhidmatan dalam talian",
    "Isi semula Perkhidmatan Pelanggan",
    "Perkhidmatan Pelanggan Talian"
  ],
  audit: {
    default: [
      "Tugas Audit",
      "Terima Pengguna",
      "Tarikh Pengambilan",
      "Dikemas kini",
      "Audit"
    ],
    tabs: ["memproses", "ditinjau", "selesai", "Gagal"],
    info: [
      "Perincian ulasan",
      "Tajuk Tugas",
      "Jumlah Tugas",
      "Orang telah selesai",
      "{Num} tempat yang tinggal",
      "perincian misi",
      "Maklumat Pautan",
      "Contoh Audit",
      "Terima Pengguna",
      "terima",
      "keadaan selesai",
      "Kirim Sampel",
      "Pengguna tidak menghantar sampel",
      "Dikemas kini",
      "memuat ...",
      "Berniat jahat",
      "Percubaan semula",
      "kegagalan",
      "kejayaan",
      "Tugas Audit",
      "Arahan Ulasan",
      "Sila masukkan keterangan ulasan",
      "Tugas yang diserahkan tidak memenuhi syarat dan perlu diserahkan untuk diperiksa",
      "Tahniah atas penyelesaian misi, terus bekerja keras",
      "Tangkapan skrin halaman tugas yang dihantar salah, tugas gagal",
      "Kirim tugas dengan jahat, tugas gagal"
    ],
  },
  postRecord: [
    "Pengurusan Pelepasan",
    "lepaskan",
    "Jumlah",
    "selesai",
    "tarikh akhir",
    "Audit",
    "Batal",
    "edit"
  ],
  wallet: {
    default: [
      "Dompet saya",
      "Isi semula",
      "tarik balik",
      "Isi semula rekod",
      "Rekod pengeluaran",
      "Caj",
      "menyebut",
      "Alipay"
    ],
    label: [
      "Kaedah pengeluaran",
      "Jumlah Pengeluaran",
      "Kata Laluan Dana",
      "serah",
      "nombor telefon",
      "peti mel",
      "IFSC",
      "Bank pengeluaran",
      "Yuran pengeluaran"
    ],
    placeholder: [
      "Pilih kaedah pengeluaran",
      "Sila masukkan jumlah pengeluaran",
      "Sila masukkan kata laluan dana",
      "Sila pilih kaedah pengeluaran",
      "Sila masukkan nombor telefon bimbit penerima",
      "Sila masukkan alamat e-mel penerima pembayaran",
      "Sila masukkan IFSC penerima pembayaran",
      "Sila pilih bank pengeluaran"
    ],
    msg: [
      "Anda belum menetapkan kata laluan dana, silakan tetapkan dulu",
      "Anda belum mengikat kad bank, sila ikat terlebih dahulu"
    ],
    info: {
      currentBalance: "Baki Semasa",
      withdrawAmount: "Jumlah Pengeluaran",
      fee: "Yuran",
      totalDeduction: "Jumlah Potongan"
    },
    warning: {
      insufficientFunds: "Baki Tidak Mencukupi",
      shortfall: "Kekurangan",
      insufficientBalance: "Baki tidak mencukupi! Jumlah pengeluaran {currency}{withdrawAmount}, baki semasa {currency}{currentBalance}, kekurangan {currency}{shortfall}",
      insufficientBalanceWithFee: "Baki tidak mencukupi! Jumlah pengeluaran {currency}{withdrawAmount} + yuran {currency}{fee} = jumlah {currency}{totalRequired}, baki semasa {currency}{currentBalance}, kekurangan {currency}{shortfall}"
    },
  },
  recharge: {
    default: [
      "Isi semula",
      "Butiran Isi Ulang",
      "Isi semula rekod",
      "Baki yang ada {wang}, sila pilih kaedah pengisian semula",
      "Jumlah minimum untuk satu transaksi adalah {currency} {min}, maksimum adalah {currency} {max}, dan biaya pengendalian adalah {fee}%",
      "sedang menghantar ...",
      "Isi semula sekarang",
      "Kembali",
      "Memuatkan..."
    ],
    info: [
      "Jumlah isi semula",
      "Nombor pesanan",
      "Bank penerima",
      "Akaun penerima",
      "Penerima",
      "Nama pembayar",
      "Nombor telefon pembayar",
      "Akaun UPI pembayar",
      "E-mel pembayar",
      "Nama bank",
      "Jenis akaun",
      "Catatan"
    ],
    placeholder: [
      "Sila masukkan jumlah isi semula",
      "Sila pilih saluran isi semula",
      "Sila masukkan nama pemindahan",
      "Jumlah minimum tunggal ialah {currency}{min}",
      "Jumlah maksimum tunggal ialah {currency}{max}",
      "Sila masukkan nama pembayar",
      "Sila masukkan nombor telefon pembayar, tambah kod antarabangsa, cth 86",
      "Sila masukkan akaun UPI pembayar",
      "Sila masukkan e-mel pembayar"
    ],
    label: [
      "Jumlah isi semula",
      "Saluran isi semula",
      "Nama pemindahan",
      "Nama pembayar",
      "Nombor telefon pembayar",
      "Akaun UPI pembayar",
      "E-mel pembayar"
    ],
    tips: [
      "Sila pilih kaedah berikut untuk memindahkan jumlah yang sepadan untuk mengelakkan kelewatan dalam kutipan kewangan<br>Sila muat naik tangkapan skrin pemindahan sebagai bukti pengesahan selepas pemindahan",
      "Tidak perlu menambah kawan, imbas kod QR untuk memindahkan wang kepada saya",
      "Sila lengkapkan pemindahan mengikut maklumat di bawah",
      "Petua: Selepas pembayaran berjaya, sila hubungi perkhidmatan pelanggan dalam talian dan berikan akaun ahli anda, jumlah isi semula, nombor pesanan, akaun pendeposit, masa isi semula; untuk memudahkan penambahan dana tepat pada masanya oleh kewangan",
      "Nota: Ahli sila hantar setiap pembayaran pemindahan sekali",
      "Sila muat naik tangkapan skrin pemindahan sebagai bukti pengesahan selepas pemindahan",
      "Sila lengkapkan pembayaran dalam masa yang ditetapkan",
      "Pembayaran tamat masa, sila hantar semula",
      "Pembayaran berjaya, sila tunggu pemprosesan"
    ]
  },
  dialog: [
    "segera",
    "tentukan",
    "sedang menghantar ...",
    "Salinan berjaya",
    "Versi sistem IOS rendah dan tidak menyokong",
    "Mendaftar ...",
    "memuat ...",
    "Nilai penuh"
  ],
  lineList: ["Pemilihan Garis", "Talian Semasa", "garis"],
  newLc: [
    "Pemindahan dalam kaedah",
    "Imbangan akaun",
    "Masa storan",
    "hari",
    "Daftar setiap hari",
    "Jumlah deposit",
    "Sila masukkan jumlah deposit",
    "keuntungan",
    "Hantar",
    "Kewangan keseimbangan",
    "Sila isi maklumat",
    "Sila pilih produk dahulu",
    "rekod"
  ],
  newLcList: [
    "Rekod pembelian",
    "Tiada rekod",
    "keuntungan",
    "jumlah wang",
    "Masa pembelian",
    "keadaan"
  ],
  wheel: [
    "Tahniah kerana menang {name}. Sistem telah menghantar bilangan hadiah",
    "Tahniah kerana menang {name}. Sila hubungi pegawai perkhidmatan pelanggan untuk menerima penghargaan",
    "Teruskan usaha anda, dan pemenang akan menjadi kali berikutnya",
    "Keterangan loteri",
    "menang",
    "Jadual Putaran Besar",
    "Baki peluang cabutan: ",
    "Mula",
    "Baki peluang cabutan tidak mencukupi"
  ],
  footer: ["Rumah", "Tugas", "", "VIP", "Saya", "Keuntungan"],
  appMsg: [
    "Bersedia untuk memuat turun kemas kini...",
    "Muat turun selesai. Adakah untuk memasang pakej kemas kini?",
    "Gagal memuat turun kemas kini",
    "{num}% dimuat turun",
    "Mengemas kini...",
    "Kemas kini berjaya. Akan dimulakan semula tidak lama lagi",
    "Kemas kini gagal",
    "Tekan lagi untuk keluar dari aplikasi"
  ],
  appDown: [
    "Muat Turun Aplikasi",
    "Imbas kod QR untuk memuat turun aplikasi",
    "Simpan kod QR",
    "Kod QR berjaya disimpan",
    "Gagal menyimpan kod QR, sila cuba beberapa kali lagi atau simpan tangkapan skrin"
  ],
  buyVip: [
    "Ejen untuk membeli VIP",
    "Akaun cas semula",
    "Sila masukkan nombor akaun cas semula",
    "Cas semula VIP",
    "Sila pilih cas semula VIP",
    "Adakah anda pasti untuk mengecas semula {grade} untuk ahli {user}?",
    "Hantar"
  ],
  Activity: [
    "Like Share Karnival Jutaan Ringgit",
    "Halaman sedang dimuatkan..."
  ],
  usdt: ["Alamat bank", "Ikat alamat bank", "Tambah alamat bank"],
  wheelRecords: {
    title: "Rekod Kemenangan",
    unknownPrize: "Hadiah Tidak Diketahui",
    times: " kali",
    prizeTypes: {
      task: "Ganjaran Tugasan",
      money: "Ganjaran Syiling",
      other: "Ganjaran Lain",
      thankYou: "Terima Kasih Kerana Menyertai"
    },
    status: {
      received: "Diterima",
      pending: "Menunggu",
      unknown: "Tidak Diketahui"
    }
  },

  // Pembayaran Global
  globalPay: {
    title: "Pembayaran Global",
    description: "Pilih kaedah pembayaran anda untuk top up yang selamat dan mudah",
    selectCountry: "Pilih Negara",
    selectCountryPlaceholder: "Sila pilih negara",
    selectCountrySubtitle: "Pilih negara atau wilayah anda",
    selectPaymentMethod: "Pilih Kaedah Pembayaran",
    selectPaymentSubtitle: "Pilih kaedah pembayaran pilihan anda",
    paymentAmount: "Jumlah Pembayaran",
    enterAmountSubtitle: "Masukkan jumlah yang ingin anda top up",
    orderNumber: "Nombor Pesanan",
    paymentStatus: "Status Pembayaran",

    steps: {
      selectCountry: "Pilih Negara",
      selectPayment: "Pilih Pembayaran",
      enterAmount: "Masukkan Jumlah"
    },

    quickAmounts: "Jumlah Pantas",
    amountRange: "Julat Jumlah",
    orderSummary: "Ringkasan Pesanan",
    country: "Negara",
    paymentMethod: "Kaedah Pembayaran",
    amount: "Jumlah",
    fee: "Yuran",
    feeRate: "Kadar Yuran",
    total: "Jumlah",

    tabs: {
      select: "Pilih Pembayaran",
      processing: "Memproses",
      completed: "Selesai"
    },

    countries: {
      ID: "Indonesia",
      IN: "India",
      TH: "Thailand",
      VN: "Vietnam",
      MY: "Malaysia",
      BR: "Brazil",
      CN: "China",
      TW: "Taiwan",
      HK: "Hong Kong",
      PH: "Filipina",
      SG: "Singapura"
    },

    paymentMethods: {
      wallet: "Dompet Digital",
      bank: "Pindahan Bank",
      card: "Kad Bank",
      scan: "Pembayaran QR",
      online: "Perbankan Dalam Talian",
      upi: "Pembayaran UPI",
      qris: "Pembayaran QRIS",
      ovo: "Dompet OVO",
      dana: "Dompet DANA",
      gopay: "Dompet GoPay",
      shopeepay: "ShopeePay",
      linkaja: "LinkAja",
      paytm: "Paytm",
      phonepe: "PhonePe",
      googlepay: "Google Pay",
      truemoney: "TrueMoney",
      promptpay: "PromptPay",
      momo: "MoMo",
      zalopay: "ZaloPay",
      grabpay: "GrabPay",
      boost: "Boost",
      tng: "Touch 'n Go",
      pix: "PIX",
      boleto: "Boleto"
    },

    status: {
      pending: "Menunggu",
      processing: "Memproses",
      completed: "Selesai",
      failed: "Pembayaran Gagal",
      cancelled: "Dibatalkan",
      expired: "Tamat Tempoh"
    },

    messages: {
      loading: "Memuatkan...",
      selectCountryFirst: "Sila pilih negara terlebih dahulu",
      selectPaymentMethodFirst: "Sila pilih kaedah pembayaran terlebih dahulu",
      enterAmount: "Sila masukkan jumlah pembayaran",
      noPaymentMethods: "Tiada kaedah pembayaran tersedia untuk negara ini",
      processing: "Memproses...",
      amountTooLow: "Jumlah pembayaran tidak boleh kurang daripada {min}",
      amountTooHigh: "Jumlah pembayaran tidak boleh melebihi {max}",
      orderCreated: "Pesanan berjaya dibuat",
      paymentSuccess: "Pembayaran berjaya",
      paymentFailed: "Pembayaran gagal",
      networkError: "Ralat rangkaian, sila cuba lagi",
      redirecting: "Mengalihkan ke halaman pembayaran..."
    },

    buttons: {
      payNow: "Bayar Sekarang",
      cancel: "Batal",
      retry: "Cuba Lagi",
      back: "Kembali",
      confirm: "Sahkan",
      selectCountry: "Pilih Negara",
      selectPayment: "Pilih Kaedah Pembayaran"
    },

    selectBank: "Pilih Bank",

    placeholders: {
      searchCountry: "Cari Negara",
      enterAmount: "Masukkan Jumlah"
    },

    tips: {
      selectCountryTip: "Sila pilih negara anda untuk memaparkan kaedah pembayaran yang tersedia",
      paymentMethodTip: "Pilih kaedah pembayaran yang paling sesuai untuk anda",
      amountTip: "Minimum {min}, Maksimum {max} setiap transaksi",
      processingTip: "Pembayaran sedang diproses, jangan tutup halaman",
      successTip: "Pembayaran berjaya, dana akan masuk dalam 5-10 minit"
    }
  }
};
